---
title: "OpenDrive"
description: "Rclone docs for OpenDrive"
versionIntroduced: "v1.42"
---

# {{< icon "fa fa-file" >}} OpenDrive

Paths are specified as `remote:path`

Paths may be as deep as required, e.g. `remote:directory/subdirectory`.

## Configuration

Here is an example of how to make a remote called `remote`.  First run:

     rclone config

This will guide you through an interactive setup process:

```
n) New remote
d) Delete remote
q) Quit config
e/n/d/q> n
name> remote
Type of storage to configure.
Choose a number from below, or type in your own value
[snip]
XX / OpenDrive
   \ "opendrive"
[snip]
Storage> opendrive
Username
username>
Password
y) Yes type in my own password
g) Generate random password
y/g> y
Enter the password:
password:
Confirm the password:
password:
--------------------
[remote]
username =
password = *** ENCRYPTED ***
--------------------
y) Yes this is OK
e) Edit this remote
d) Delete this remote
y/e/d> y
```

List directories in top level of your OpenDrive

    rclone lsd remote:

List all the files in your OpenDrive

    rclone ls remote:

To copy a local directory to an OpenDrive directory called backup

    rclone copy /home/<USER>

### Modified time and MD5SUMs

OpenDrive allows modification times to be set on objects accurate to 1
second. These will be used to detect whether objects need syncing or
not.

### Restricted filename characters

| Character | Value | Replacement |
| --------- |:-----:|:-----------:|
| NUL       | 0x00  | ␀           |
| /         | 0x2F  | ／          |
| "         | 0x22  | ＂          |
| *         | 0x2A  | ＊          |
| :         | 0x3A  | ：          |
| <         | 0x3C  | ＜          |
| >         | 0x3E  | ＞          |
| ?         | 0x3F  | ？          |
| \         | 0x5C  | ＼          |
| \|        | 0x7C  | ｜          |

File names can also not begin or end with the following characters.
These only get replaced if they are the first or last character in the name:

| Character | Value | Replacement |
| --------- |:-----:|:-----------:|
| SP        | 0x20  | ␠           |
| HT        | 0x09  | ␉           |
| LF        | 0x0A  | ␊           |
| VT        | 0x0B  | ␋           |
| CR        | 0x0D  | ␍           |


Invalid UTF-8 bytes will also be [replaced](/overview/#invalid-utf8),
as they can't be used in JSON strings.

{{< rem autogenerated options start" - DO NOT EDIT - instead edit fs.RegInfo in backend/opendrive/opendrive.go then run make backenddocs" >}}
### Standard options

Here are the Standard options specific to opendrive (OpenDrive).

#### --opendrive-username

Username.

Properties:

- Config:      username
- Env Var:     RCLONE_OPENDRIVE_USERNAME
- Type:        string
- Required:    true

#### --opendrive-password

Password.

**NB** Input to this must be obscured - see [rclone obscure](/commands/rclone_obscure/).

Properties:

- Config:      password
- Env Var:     RCLONE_OPENDRIVE_PASSWORD
- Type:        string
- Required:    true

### Advanced options

Here are the Advanced options specific to opendrive (OpenDrive).

#### --opendrive-encoding

The encoding for the backend.

See the [encoding section in the overview](/overview/#encoding) for more info.

Properties:

- Config:      encoding
- Env Var:     RCLONE_OPENDRIVE_ENCODING
- Type:        MultiEncoder
- Default:     Slash,LtGt,DoubleQuote,Colon,Question,Asterisk,Pipe,BackSlash,LeftSpace,LeftCrLfHtVt,RightSpace,RightCrLfHtVt,InvalidUtf8,Dot

#### --opendrive-chunk-size

Files will be uploaded in chunks this size.

Note that these chunks are buffered in memory so increasing them will
increase memory use.

Properties:

- Config:      chunk_size
- Env Var:     RCLONE_OPENDRIVE_CHUNK_SIZE
- Type:        SizeSuffix
- Default:     10Mi

{{< rem autogenerated options stop >}}

## Limitations

Note that OpenDrive is case insensitive so you can't have a
file called "Hello.doc" and one called "hello.doc".

There are quite a few characters that can't be in OpenDrive file
names.  These can't occur on Windows platforms, but on non-Windows
platforms they are common.  Rclone will map these names to and from an
identical looking unicode equivalent.  For example if a file has a `?`
in it will be mapped to `？` instead.

`rclone about` is not supported by the OpenDrive backend. Backends without
this capability cannot determine free space for an rclone mount or
use policy `mfs` (most free space) as a member of an rclone union
remote.

See [List of backends that do not support rclone about](https://rclone.org/overview/#optional-features) and [rclone about](https://rclone.org/commands/rclone_about/)



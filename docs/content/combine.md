---
title: "Combine"
description: "Combine several remotes into one"
versionIntroduced: "v1.59"
---

# {{< icon "fa fa-folder-plus" >}} Combine

The `combine` backend joins remotes together into a single directory
tree.

For example you might have a remote for images on one provider:

```
$ rclone tree s3:imagesbucket
/
├── image1.jpg
└── image2.jpg
```

And a remote for files on another:

```
$ rclone tree drive:important/files
/
├── file1.txt
└── file2.txt
```

The `combine` backend can join these together into a synthetic
directory structure like this:

```
$ rclone tree combined:
/
├── files
│   ├── file1.txt
│   └── file2.txt
└── images
    ├── image1.jpg
    └── image2.jpg
```

You'd do this by specifying an `upstreams` parameter in the config
like this

    upstreams = images=s3:imagesbucket files=drive:important/files

During the initial setup with `rclone config` you will specify the
upstreams remotes as a space separated list. The upstream remotes can
either be a local paths or other remotes.

## Configuration

Here is an example of how to make a combine called `remote` for the
example above. First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> remote
Option Storage.
Type of storage to configure.
Choose a number from below, or type in your own value.
...
XX / Combine several remotes into one
   \ (combine)
...
Storage> combine
Option upstreams.
Upstreams for combining
These should be in the form
    dir=remote:path dir2=remote2:path
Where before the = is specified the root directory and after is the remote to
put there.
Embedded spaces can be added using quotes
    "dir=remote:path with space" "dir2=remote2:path with space"
Enter a fs.SpaceSepList value.
upstreams> images=s3:imagesbucket files=drive:important/files
--------------------
[remote]
type = combine
upstreams = images=s3:imagesbucket files=drive:important/files
--------------------
y) Yes this is OK (default)
e) Edit this remote
d) Delete this remote
y/e/d> y
```

### Configuring for Google Drive Shared Drives

Rclone has a convenience feature for making a combine backend for all
the shared drives you have access to.

Assuming your main (non shared drive) Google drive remote is called
`drive:` you would run

    rclone backend -o config drives drive:

This would produce something like this:

    [My Drive]
    type = alias
    remote = drive,team_drive=0ABCDEF-01234567890,root_folder_id=:

    [Test Drive]
    type = alias
    remote = drive,team_drive=0ABCDEFabcdefghijkl,root_folder_id=:

    [AllDrives]
    type = combine
    upstreams = "My Drive=My Drive:" "Test Drive=Test Drive:"

If you then add that config to your config file (find it with `rclone
config file`) then you can access all the shared drives in one place
with the `AllDrives:` remote.

See [the Google Drive docs](/drive/#drives) for full info.

{{< rem autogenerated options start" - DO NOT EDIT - instead edit fs.RegInfo in backend/combine/combine.go then run make backenddocs" >}}
### Standard options

Here are the Standard options specific to combine (Combine several remotes into one).

#### --combine-upstreams

Upstreams for combining

These should be in the form

    dir=remote:path dir2=remote2:path

Where before the = is specified the root directory and after is the remote to
put there.

Embedded spaces can be added using quotes

    "dir=remote:path with space" "dir2=remote2:path with space"



Properties:

- Config:      upstreams
- Env Var:     RCLONE_COMBINE_UPSTREAMS
- Type:        SpaceSepList
- Default:     

### Metadata

Any metadata supported by the underlying remote is read and written.

See the [metadata](/docs/#metadata) docs for more info.

{{< rem autogenerated options stop >}}

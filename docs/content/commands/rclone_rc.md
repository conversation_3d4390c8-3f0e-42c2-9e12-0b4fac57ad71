---
title: "rclone rc"
description: "Run a command against a running rclone."
slug: rclone_rc
url: /commands/rclone_rc/
versionIntroduced: v1.40
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/rc/ and as part of making a release run "make commanddocs"
---
# rclone rc

Run a command against a running rclone.

## Synopsis



This runs a command against a running rclone.  Use the `--url` flag to
specify an non default URL to connect on.  This can be either a
":port" which is taken to mean "http://localhost:port" or a
"host:port" which is taken to mean "http://host:port"

A username and password can be passed in with `--user` and `--pass`.

Note that `--rc-addr`, `--rc-user`, `--rc-pass` will be read also for
`--url`, `--user`, `--pass`.

Arguments should be passed in as parameter=value.

The result will be returned as a JSON object by default.

The `--json` parameter can be used to pass in a JSON blob as an input
instead of key=value arguments.  This is the only way of passing in
more complicated values.

The `-o`/`--opt` option can be used to set a key "opt" with key, value
options in the form `-o key=value` or `-o key`. It can be repeated as
many times as required. This is useful for rc commands which take the
"opt" parameter which by convention is a dictionary of strings.

    -o key=value -o key2

Will place this in the "opt" value

    {"key":"value", "key2","")


The `-a`/`--arg` option can be used to set strings in the "arg" value. It
can be repeated as many times as required. This is useful for rc
commands which take the "arg" parameter which by convention is a list
of strings.

    -a value -a value2

Will place this in the "arg" value

    ["value", "value2"]

Use `--loopback` to connect to the rclone instance running `rclone rc`.
This is very useful for testing commands without having to run an
rclone rc server, e.g.:

    rclone rc --loopback operations/about fs=/

Use `rclone rc` to see a list of all possible commands.

```
rclone rc commands parameter [flags]
```

## Options

```
  -a, --arg stringArray   Argument placed in the "arg" array
  -h, --help              help for rc
      --json string       Input JSON - use instead of key=value args
      --loopback          If set connect to this rclone instance not via HTTP
      --no-output         If set, don't output the JSON result
  -o, --opt stringArray   Option in the form name=value or name placed in the "opt" array
      --pass string       Password to use to connect to rclone remote control
      --url string        URL to connect to rclone remote control (default "http://localhost:5572/")
      --user string       Username to use to rclone remote control
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


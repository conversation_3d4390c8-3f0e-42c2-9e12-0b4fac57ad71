---
title: "rclone config paths"
description: "Show paths used for configuration, cache, temp etc."
slug: rclone_config_paths
url: /commands/rclone_config_paths/
versionIntroduced: v1.57
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/config/paths/ and as part of making a release run "make commanddocs"
---
# rclone config paths

Show paths used for configuration, cache, temp etc.

```
rclone config paths [flags]
```

## Options

```
  -h, --help   help for paths
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone config](/commands/rclone_config/)	 - Enter an interactive configuration session.


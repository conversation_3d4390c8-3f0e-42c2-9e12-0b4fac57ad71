---
title: "rclone check"
description: "Checks the files in the source and destination match."
slug: rclone_check
url: /commands/rclone_check/
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/check/ and as part of making a release run "make commanddocs"
---
# rclone check

Checks the files in the source and destination match.

## Synopsis


Checks the files in the source and destination match.  It compares
sizes and hashes (MD5 or SHA1) and logs a report of files that don't
match.  It doesn't alter the source or destination.

For the [crypt](/crypt/) remote there is a dedicated command,
[cryptcheck](/commands/rclone_cryptcheck/), that are able to check
the checksums of the encrypted files.

If you supply the `--size-only` flag, it will only compare the sizes not
the hashes as well.  Use this for a quick check.

If you supply the `--download` flag, it will download the data from
both remotes and check them against each other on the fly.  This can
be useful for remotes that don't support hashes or if you really want
to check all the data.

If you supply the `--checkfile HASH` flag with a valid hash name,
the `source:path` must point to a text file in the SUM format.

If you supply the `--one-way` flag, it will only check that files in
the source match the files in the destination, not the other way
around. This means that extra files in the destination that are not in
the source will not be detected.

The `--differ`, `--missing-on-dst`, `--missing-on-src`, `--match`
and `--error` flags write paths, one per line, to the file name (or
stdout if it is `-`) supplied. What they write is described in the
help below. For example `--differ` will write all paths which are
present on both the source and destination but different.

The `--combined` flag will write a file (or stdout) which contains all
file paths with a symbol and then a space and then the path to tell
you what happened to it. These are reminiscent of diff files.

- `= path` means path was found in source and destination and was identical
- `- path` means path was missing on the source, so only in the destination
- `+ path` means path was missing on the destination, so only in the source
- `* path` means path was present in source and destination but different.
- `! path` means there was an error reading or hashing the source or dest.

The default number of parallel checks is N=8. See the [--checkers=N](/docs/#checkers-n) option
for more information.

```
rclone check source:path dest:path [flags]
```

## Options

```
  -C, --checkfile string        Treat source:path as a SUM file with hashes of given type
      --combined string         Make a combined report of changes to this file
      --differ string           Report all non-matching files to this file
      --download                Check by downloading rather than with hash
      --error string            Report all files with errors (hashing or reading) to this file
  -h, --help                    help for check
      --match string            Report all matching files to this file
      --missing-on-dst string   Report all files missing from the destination to this file
      --missing-on-src string   Report all files missing from the source to this file
      --one-way                 Check one way only, source files must exist on remote
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


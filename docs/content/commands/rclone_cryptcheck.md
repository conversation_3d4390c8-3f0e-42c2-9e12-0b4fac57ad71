---
title: "rclone cryptcheck"
description: "Cryptcheck checks the integrity of an encrypted remote."
slug: rclone_cryptcheck
url: /commands/rclone_cryptcheck/
versionIntroduced: v1.36
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/cryptcheck/ and as part of making a release run "make commanddocs"
---
# rclone cryptcheck

Cryptcheck checks the integrity of an encrypted remote.

## Synopsis


rclone cryptcheck checks a remote against an [encrypted](/crypt/) remote.
This is the equivalent of running rclone [check](/commands/rclone_check/),
but able to check the checksums of the encrypted remote.

For it to work the underlying remote of the encryptedremote must support
some kind of checksum.

It works by reading the nonce from each file on the encryptedremote: and
using that to encrypt each file on the remote:.  It then checks the
checksum of the underlying file on the ercryptedremote: against the
checksum of the file it has just encrypted.

Use it like this

    rclone cryptcheck /path/to/files encryptedremote:path

You can use it like this also, but that will involve downloading all
the files in remote:path.

    rclone cryptcheck remote:path encryptedremote:path

After it has run it will log the status of the encryptedremote:.

If you supply the `--one-way` flag, it will only check that files in
the source match the files in the destination, not the other way
around. This means that extra files in the destination that are not in
the source will not be detected.

The `--differ`, `--missing-on-dst`, `--missing-on-src`, `--match`
and `--error` flags write paths, one per line, to the file name (or
stdout if it is `-`) supplied. What they write is described in the
help below. For example `--differ` will write all paths which are
present on both the source and destination but different.

The `--combined` flag will write a file (or stdout) which contains all
file paths with a symbol and then a space and then the path to tell
you what happened to it. These are reminiscent of diff files.

- `= path` means path was found in source and destination and was identical
- `- path` means path was missing on the source, so only in the destination
- `+ path` means path was missing on the destination, so only in the source
- `* path` means path was present in source and destination but different.
- `! path` means there was an error reading or hashing the source or dest.

The default number of parallel checks is N=8. See the [--checkers=N](/docs/#checkers-n) option
for more information.

```
rclone cryptcheck remote:path encryptedremote:path [flags]
```

## Options

```
      --combined string         Make a combined report of changes to this file
      --differ string           Report all non-matching files to this file
      --error string            Report all files with errors (hashing or reading) to this file
  -h, --help                    help for cryptcheck
      --match string            Report all matching files to this file
      --missing-on-dst string   Report all files missing from the destination to this file
      --missing-on-src string   Report all files missing from the source to this file
      --one-way                 Check one way only, source files must exist on remote
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


---
title: "rclone deletefile"
description: "Remove a single file from remote."
slug: rclone_deletefile
url: /commands/rclone_deletefile/
versionIntroduced: v1.42
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/deletefile/ and as part of making a release run "make commanddocs"
---
# rclone deletefile

Remove a single file from remote.

## Synopsis


Remove a single file from remote.  Unlike `delete` it cannot be used to
remove a directory and it doesn't obey include/exclude filters - if the specified file exists,
it will always be removed.


```
rclone deletefile remote:path [flags]
```

## Options

```
  -h, --help   help for deletefile
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


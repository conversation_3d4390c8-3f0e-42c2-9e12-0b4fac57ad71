---
title: "rclone test histogram"
description: "Makes a histogram of file name characters."
slug: rclone_test_histogram
url: /commands/rclone_test_histogram/
versionIntroduced: v1.55
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/test/histogram/ and as part of making a release run "make commanddocs"
---
# rclone test histogram

Makes a histogram of file name characters.

## Synopsis

This command outputs JSON which shows the histogram of characters used
in filenames in the remote:path specified.

The data doesn't contain any identifying information but is useful for
the rclone developers when developing filename compression.


```
rclone test histogram [remote:path] [flags]
```

## Options

```
  -h, --help   help for histogram
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone test](/commands/rclone_test/)	 - Run a test command


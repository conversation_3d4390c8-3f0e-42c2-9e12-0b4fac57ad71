---
title: "rclone genautocomplete"
description: "Output completion script for a given shell."
slug: rclone_genautocomplete
url: /commands/rclone_genautocomplete/
versionIntroduced: v1.33
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/genautocomplete/ and as part of making a release run "make commanddocs"
---
# rclone genautocomplete

Output completion script for a given shell.

## Synopsis


Generates a shell completion script for rclone.
Run with `--help` to list the supported shells.


## Options

```
  -h, --help   help for genautocomplete
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.
* [rclone genautocomplete bash](/commands/rclone_genautocomplete_bash/)	 - Output bash completion script for rclone.
* [rclone genautocomplete fish](/commands/rclone_genautocomplete_fish/)	 - Output fish completion script for rclone.
* [rclone genautocomplete zsh](/commands/rclone_genautocomplete_zsh/)	 - Output zsh completion script for rclone.


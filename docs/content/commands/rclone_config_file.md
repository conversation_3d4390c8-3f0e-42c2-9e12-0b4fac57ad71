---
title: "rclone config file"
description: "Show path of configuration file in use."
slug: rclone_config_file
url: /commands/rclone_config_file/
versionIntroduced: v1.38
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/config/file/ and as part of making a release run "make commanddocs"
---
# rclone config file

Show path of configuration file in use.

```
rclone config file [flags]
```

## Options

```
  -h, --help   help for file
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone config](/commands/rclone_config/)	 - Enter an interactive configuration session.


---
title: "rclone test changenotify"
description: "Log any change notify requests for the remote passed in."
slug: rclone_test_changenotify
url: /commands/rclone_test_changenotify/
versionIntroduced: v1.56
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/test/changenotify/ and as part of making a release run "make commanddocs"
---
# rclone test changenotify

Log any change notify requests for the remote passed in.

```
rclone test changenotify remote: [flags]
```

## Options

```
  -h, --help                     help for changenotify
      --poll-interval Duration   Time to wait between polling for changes (default 10s)
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone test](/commands/rclone_test/)	 - Run a test command


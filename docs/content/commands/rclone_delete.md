---
title: "rclone delete"
description: "Remove the files in path."
slug: rclone_delete
url: /commands/rclone_delete/
versionIntroduced: v1.27
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/delete/ and as part of making a release run "make commanddocs"
---
# rclone delete

Remove the files in path.

## Synopsis


Remove the files in path.  Unlike [purge](/commands/rclone_purge/) it
obeys include/exclude filters so can be used to selectively delete files.

`rclone delete` only deletes files but leaves the directory structure
alone. If you want to delete a directory and all of its contents use
the [purge](/commands/rclone_purge/) command.

If you supply the `--rmdirs` flag, it will remove all empty directories along with it.
You can also use the separate command [rmdir](/commands/rclone_rmdir/) or
[rmdirs](/commands/rclone_rmdirs/) to delete empty directories only.

For example, to delete all files bigger than 100 MiB, you may first want to
check what would be deleted (use either):

    rclone --min-size 100M lsl remote:path
    rclone --dry-run --min-size 100M delete remote:path

Then proceed with the actual delete:

    rclone --min-size 100M delete remote:path

That reads "delete everything with a minimum size of 100 MiB", hence
delete all files bigger than 100 MiB.

**Important**: Since this can cause data loss, test first with the
`--dry-run` or the `--interactive`/`-i` flag.


```
rclone delete remote:path [flags]
```

## Options

```
  -h, --help     help for delete
      --rmdirs   rmdirs removes empty directories but leaves root intact
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


---
title: "rclone gendocs"
description: "Output markdown docs for rclone to the directory supplied."
slug: rclone_gendocs
url: /commands/rclone_gendocs/
versionIntroduced: v1.33
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/gendocs/ and as part of making a release run "make commanddocs"
---
# rclone gendocs

Output markdown docs for rclone to the directory supplied.

## Synopsis


This produces markdown docs for the rclone commands to the directory
supplied.  These are in a format suitable for hugo to render into the
rclone.org website.

```
rclone gendocs output_directory [flags]
```

## Options

```
  -h, --help   help for gendocs
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


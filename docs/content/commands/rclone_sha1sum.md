---
title: "rclone sha1sum"
description: "Produces an sha1sum file for all the objects in the path."
slug: rclone_sha1sum
url: /commands/rclone_sha1sum/
versionIntroduced: v1.27
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/sha1sum/ and as part of making a release run "make commanddocs"
---
# rclone sha1sum

Produces an sha1sum file for all the objects in the path.

## Synopsis


Produces an sha1sum file for all the objects in the path.  This
is in the same format as the standard sha1sum tool produces.

By default, the hash is requested from the remote.  If SHA-1 is
not supported by the remote, no hash will be returned.  With the
download flag, the file will be downloaded from the remote and
hashed locally enabling SHA-1 for any remote.

For other algorithms, see the [hashsum](/commands/rclone_hashsum/)
command. Running `rclone sha1sum remote:path` is equivalent
to running `rclone hashsum SHA1 remote:path`.

This command can also hash data received on standard input (stdin),
by not passing a remote:path, or by passing a hyphen as remote:path
when there is data to read (if not, the hyphen will be treated literally,
as a relative path).

This command can also hash data received on STDIN, if not passing
a remote:path.


```
rclone sha1sum remote:path [flags]
```

## Options

```
      --base64               Output base64 encoded hashsum
  -C, --checkfile string     Validate hashes against a given SUM file instead of printing them
      --download             Download the file and hash it locally; if this flag is not specified, the hash is requested from the remote
  -h, --help                 help for sha1sum
      --output-file string   Output hashsums to a file rather than the terminal
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


---
title: "rclone backend"
description: "Run a backend-specific command."
slug: rclone_backend
url: /commands/rclone_backend/
versionIntroduced: v1.52
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/backend/ and as part of making a release run "make commanddocs"
---
# rclone backend

Run a backend-specific command.

## Synopsis


This runs a backend-specific command. The commands themselves (except
for "help" and "features") are defined by the backends and you should
see the backend docs for definitions.

You can discover what commands a backend implements by using

    rclone backend help remote:
    rclone backend help <backendname>

You can also discover information about the backend using (see
[operations/fsinfo](/rc/#operations-fsinfo) in the remote control docs
for more info).

    rclone backend features remote:

Pass options to the backend command with -o. This should be key=value or key, e.g.:

    rclone backend stats remote:path stats -o format=json -o long

Pass arguments to the backend by placing them on the end of the line

    rclone backend cleanup remote:path file1 file2 file3

Note to run these commands on a running backend then see
[backend/command](/rc/#backend-command) in the rc docs.


```
rclone backend <command> remote:path [opts] <args> [flags]
```

## Options

```
  -h, --help                 help for backend
      --json                 Always output in JSON format
  -o, --option stringArray   Option in the form name=value or name
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


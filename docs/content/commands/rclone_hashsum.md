---
title: "rclone hashsum"
description: "Produces a hashsum file for all the objects in the path."
slug: rclone_hashsum
url: /commands/rclone_hashsum/
versionIntroduced: v1.41
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/hashsum/ and as part of making a release run "make commanddocs"
---
# rclone hashsum

Produces a hashsum file for all the objects in the path.

## Synopsis


Produces a hash file for all the objects in the path using the hash
named.  The output is in the same format as the standard
md5sum/sha1sum tool.

By default, the hash is requested from the remote.  If the hash is
not supported by the remote, no hash will be returned.  With the
download flag, the file will be downloaded from the remote and
hashed locally enabling any hash for any remote.

For the MD5 and SHA1 algorithms there are also dedicated commands,
[md5sum](/commands/rclone_md5sum/) and [sha1sum](/commands/rclone_sha1sum/).

This command can also hash data received on standard input (stdin),
by not passing a remote:path, or by passing a hyphen as remote:path
when there is data to read (if not, the hyphen will be treated literally,
as a relative path).

Run without a hash to see the list of all supported hashes, e.g.

    $ rclone hashsum
    Supported hashes are:
      * md5
      * sha1
      * whirlpool
      * crc32
      * sha256
      * dropbox
      * hidrive
      * mailru
      * quickxor

Then

    $ rclone hashsum MD5 remote:path

Note that hash names are case insensitive and values are output in lower case.


```
rclone hashsum <hash> remote:path [flags]
```

## Options

```
      --base64               Output base64 encoded hashsum
  -C, --checkfile string     Validate hashes against a given SUM file instead of printing them
      --download             Download the file and hash it locally; if this flag is not specified, the hash is requested from the remote
  -h, --help                 help for hashsum
      --output-file string   Output hashsums to a file rather than the terminal
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


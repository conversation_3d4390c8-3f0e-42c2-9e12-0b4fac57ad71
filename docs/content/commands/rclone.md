---
title: "rclone"
description: "Show help for rclone commands, flags and backends."
slug: rclone
url: /commands/rclone/
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/ and as part of making a release run "make commanddocs"
---
# rclone

Show help for rclone commands, flags and backends.

## Synopsis


Rclone syncs files to and from cloud storage providers as well as
mounting them, listing them in lots of different ways.

See the home page (https://rclone.org/) for installation, usage,
documentation, changelog and configuration walkthroughs.



```
rclone [flags]
```

## Options

```
  -h, --help   help for rclone
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone about](/commands/rclone_about/)	 - Get quota information from the remote.
* [rclone authorize](/commands/rclone_authorize/)	 - Remote authorization.
* [rclone backend](/commands/rclone_backend/)	 - Run a backend-specific command.
* [rclone bisync](/commands/rclone_bisync/)	 - Perform bidirectional synchronization between two paths.
* [rclone cat](/commands/rclone_cat/)	 - Concatenates any files and sends them to stdout.
* [rclone check](/commands/rclone_check/)	 - Checks the files in the source and destination match.
* [rclone checksum](/commands/rclone_checksum/)	 - Checks the files in the source against a SUM file.
* [rclone cleanup](/commands/rclone_cleanup/)	 - Clean up the remote if possible.
* [rclone completion](/commands/rclone_completion/)	 - Generate the autocompletion script for the specified shell
* [rclone config](/commands/rclone_config/)	 - Enter an interactive configuration session.
* [rclone copy](/commands/rclone_copy/)	 - Copy files from source to dest, skipping identical files.
* [rclone copyto](/commands/rclone_copyto/)	 - Copy files from source to dest, skipping identical files.
* [rclone copyurl](/commands/rclone_copyurl/)	 - Copy url content to dest.
* [rclone cryptcheck](/commands/rclone_cryptcheck/)	 - Cryptcheck checks the integrity of an encrypted remote.
* [rclone cryptdecode](/commands/rclone_cryptdecode/)	 - Cryptdecode returns unencrypted file names.
* [rclone dedupe](/commands/rclone_dedupe/)	 - Interactively find duplicate filenames and delete/rename them.
* [rclone delete](/commands/rclone_delete/)	 - Remove the files in path.
* [rclone deletefile](/commands/rclone_deletefile/)	 - Remove a single file from remote.
* [rclone genautocomplete](/commands/rclone_genautocomplete/)	 - Output completion script for a given shell.
* [rclone gendocs](/commands/rclone_gendocs/)	 - Output markdown docs for rclone to the directory supplied.
* [rclone hashsum](/commands/rclone_hashsum/)	 - Produces a hashsum file for all the objects in the path.
* [rclone link](/commands/rclone_link/)	 - Generate public link to file/folder.
* [rclone listremotes](/commands/rclone_listremotes/)	 - List all the remotes in the config file.
* [rclone ls](/commands/rclone_ls/)	 - List the objects in the path with size and path.
* [rclone lsd](/commands/rclone_lsd/)	 - List all directories/containers/buckets in the path.
* [rclone lsf](/commands/rclone_lsf/)	 - List directories and objects in remote:path formatted for parsing.
* [rclone lsjson](/commands/rclone_lsjson/)	 - List directories and objects in the path in JSON format.
* [rclone lsl](/commands/rclone_lsl/)	 - List the objects in path with modification time, size and path.
* [rclone md5sum](/commands/rclone_md5sum/)	 - Produces an md5sum file for all the objects in the path.
* [rclone mkdir](/commands/rclone_mkdir/)	 - Make the path if it doesn't already exist.
* [rclone mount](/commands/rclone_mount/)	 - Mount the remote as file system on a mountpoint.
* [rclone move](/commands/rclone_move/)	 - Move files from source to dest.
* [rclone moveto](/commands/rclone_moveto/)	 - Move file or directory from source to dest.
* [rclone ncdu](/commands/rclone_ncdu/)	 - Explore a remote with a text based user interface.
* [rclone obscure](/commands/rclone_obscure/)	 - Obscure password for use in the rclone config file.
* [rclone purge](/commands/rclone_purge/)	 - Remove the path and all of its contents.
* [rclone rc](/commands/rclone_rc/)	 - Run a command against a running rclone.
* [rclone rcat](/commands/rclone_rcat/)	 - Copies standard input to file on remote.
* [rclone rcd](/commands/rclone_rcd/)	 - Run rclone listening to remote control commands only.
* [rclone rmdir](/commands/rclone_rmdir/)	 - Remove the empty directory at path.
* [rclone rmdirs](/commands/rclone_rmdirs/)	 - Remove empty directories under the path.
* [rclone selfupdate](/commands/rclone_selfupdate/)	 - Update the rclone binary.
* [rclone serve](/commands/rclone_serve/)	 - Serve a remote over a protocol.
* [rclone settier](/commands/rclone_settier/)	 - Changes storage class/tier of objects in remote.
* [rclone sha1sum](/commands/rclone_sha1sum/)	 - Produces an sha1sum file for all the objects in the path.
* [rclone size](/commands/rclone_size/)	 - Prints the total size and number of objects in remote:path.
* [rclone sync](/commands/rclone_sync/)	 - Make source and dest identical, modifying destination only.
* [rclone test](/commands/rclone_test/)	 - Run a test command
* [rclone touch](/commands/rclone_touch/)	 - Create new file or change file modification time.
* [rclone tree](/commands/rclone_tree/)	 - List the contents of the remote in a tree like fashion.
* [rclone version](/commands/rclone_version/)	 - Show the version number.


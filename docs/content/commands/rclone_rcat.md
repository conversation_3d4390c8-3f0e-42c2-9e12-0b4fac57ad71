---
title: "rclone rcat"
description: "Copies standard input to file on remote."
slug: rclone_rcat
url: /commands/rclone_rcat/
versionIntroduced: v1.38
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/rcat/ and as part of making a release run "make commanddocs"
---
# rclone rcat

Copies standard input to file on remote.

## Synopsis


rclone rcat reads from standard input (stdin) and copies it to a
single remote file.

    echo "hello world" | rclone rcat remote:path/to/file
    ffmpeg - | rclone rcat remote:path/to/file

If the remote file already exists, it will be overwritten.

rcat will try to upload small files in a single request, which is
usually more efficient than the streaming/chunked upload endpoints,
which use multiple requests. Exact behaviour depends on the remote.
What is considered a small file may be set through
`--streaming-upload-cutoff`. Uploading only starts after
the cutoff is reached or if the file ends before that. The data
must fit into RAM. The cutoff needs to be small enough to adhere
the limits of your remote, please see there. Generally speaking,
setting this cutoff too high will decrease your performance.

Use the `--size` flag to preallocate the file in advance at the remote end
and actually stream it, even if remote backend doesn't support streaming.

`--size` should be the exact size of the input stream in bytes. If the
size of the stream is different in length to the `--size` passed in
then the transfer will likely fail.

Note that the upload can also not be retried because the data is
not kept around until the upload succeeds. If you need to transfer
a lot of data, you're better off caching locally and then
`rclone move` it to the destination.

```
rclone rcat remote:path [flags]
```

## Options

```
  -h, --help       help for rcat
      --size int   File size hint to preallocate (default -1)
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


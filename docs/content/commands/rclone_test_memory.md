---
title: "rclone test memory"
description: "Load all the objects at remote:path into memory and report memory stats."
slug: rclone_test_memory
url: /commands/rclone_test_memory/
versionIntroduced: v1.55
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/test/memory/ and as part of making a release run "make commanddocs"
---
# rclone test memory

Load all the objects at remote:path into memory and report memory stats.

```
rclone test memory remote:path [flags]
```

## Options

```
  -h, --help   help for memory
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone test](/commands/rclone_test/)	 - Run a test command


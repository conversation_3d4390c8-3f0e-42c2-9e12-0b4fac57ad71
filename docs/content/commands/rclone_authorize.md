---
title: "rclone authorize"
description: "Remote authorization."
slug: rclone_authorize
url: /commands/rclone_authorize/
versionIntroduced: v1.27
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/authorize/ and as part of making a release run "make commanddocs"
---
# rclone authorize

Remote authorization.

## Synopsis


Remote authorization. Used to authorize a remote or headless
rclone from a machine with a browser - use as instructed by
rclone config.

Use --auth-no-open-browser to prevent rclone to open auth
link in default browser automatically.

Use --template to generate HTML output via a custom Go template. If a blank string is provided as an argument to this flag, the default template is used.

```
rclone authorize [flags]
```

## Options

```
      --auth-no-open-browser   Do not automatically open auth link in default browser
  -h, --help                   help for authorize
      --template string        The path to a custom Go template for generating HTML responses
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


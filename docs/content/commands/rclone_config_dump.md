---
title: "rclone config dump"
description: "Dump the config file as <PERSON>SO<PERSON>."
slug: rclone_config_dump
url: /commands/rclone_config_dump/
versionIntroduced: v1.39
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/config/dump/ and as part of making a release run "make commanddocs"
---
# rclone config dump

Dump the config file as JSON.

```
rclone config dump [flags]
```

## Options

```
  -h, --help   help for dump
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone config](/commands/rclone_config/)	 - Enter an interactive configuration session.


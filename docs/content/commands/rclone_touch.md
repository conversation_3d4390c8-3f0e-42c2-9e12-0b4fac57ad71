---
title: "rclone touch"
description: "Create new file or change file modification time."
slug: rclone_touch
url: /commands/rclone_touch/
versionIntroduced: v1.39
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/touch/ and as part of making a release run "make commanddocs"
---
# rclone touch

Create new file or change file modification time.

## Synopsis


Set the modification time on file(s) as specified by remote:path to
have the current time.

If remote:path does not exist then a zero sized file will be created,
unless `--no-create` or `--recursive` is provided.

If `--recursive` is used then recursively sets the modification
time on all existing files that is found under the path. Filters are supported,
and you can test with the `--dry-run` or the `--interactive`/`-i` flag.

If `--timestamp` is used then sets the modification time to that
time instead of the current time. Times may be specified as one of:

- 'YYMMDD' - e.g. 17.10.30
- 'YYYY-MM-DDTHH:MM:SS' - e.g. 2006-01-02T15:04:05
- 'YYYY-MM-DDTHH:MM:SS.SSS' - e.g. 2006-01-02T15:04:05.123456789

Note that value of `--timestamp` is in UTC. If you want local time
then add the `--localtime` flag.


```
rclone touch remote:path [flags]
```

## Options

```
  -h, --help               help for touch
      --localtime          Use localtime for timestamp, not UTC
  -C, --no-create          Do not create the file if it does not exist (implied with --recursive)
  -R, --recursive          Recursively touch all files
  -t, --timestamp string   Use specified time instead of the current time of day
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


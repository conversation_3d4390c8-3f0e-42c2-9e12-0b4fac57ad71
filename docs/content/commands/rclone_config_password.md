---
title: "rclone config password"
description: "Update password in an existing remote."
slug: rclone_config_password
url: /commands/rclone_config_password/
versionIntroduced: v1.39
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/config/password/ and as part of making a release run "make commanddocs"
---
# rclone config password

Update password in an existing remote.

## Synopsis


Update an existing remote's password. The password
should be passed in pairs of `key` `password` or as `key=password`.
The `password` should be passed in in clear (unobscured).

For example, to set password of a remote of name myremote you would do:

    rclone config password myremote fieldname mypassword
    rclone config password myremote fieldname=mypassword

This command is obsolete now that "config update" and "config create"
both support obscuring passwords directly.


```
rclone config password name [key value]+ [flags]
```

## Options

```
  -h, --help   help for password
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone config](/commands/rclone_config/)	 - Enter an interactive configuration session.


---
title: "rclone ncdu"
description: "Explore a remote with a text based user interface."
slug: rclone_ncdu
url: /commands/rclone_ncdu/
versionIntroduced: v1.37
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/ncdu/ and as part of making a release run "make commanddocs"
---
# rclone ncdu

Explore a remote with a text based user interface.

## Synopsis


This displays a text based user interface allowing the navigation of a
remote. It is most useful for answering the question - "What is using
all my disk space?".

{{< asciinema 157793 >}}

To make the user interface it first scans the entire remote given and
builds an in memory representation.  rclone ncdu can be used during
this scanning phase and you will see it building up the directory
structure as it goes along.

You can interact with the user interface using key presses,
press '?' to toggle the help on and off. The supported keys are:

     ↑,↓ or k,j to Move
     →,l to enter
     ←,h to return
     g toggle graph
     c toggle counts
     a toggle average size in directory
     m toggle modified time
     u toggle human-readable format
     n,s,C,A,M sort by name,size,count,asize,mtime
     d delete file/directory
     v select file/directory
     V enter visual select mode
     D delete selected files/directories
     y copy current path to clipboard
     Y display current path
     ^L refresh screen (fix screen corruption)
     ? to toggle help on and off
     q/ESC/^c to quit

Listed files/directories may be prefixed by a one-character flag,
some of them combined with a description in brackets at end of line.
These flags have the following meaning:

    e means this is an empty directory, i.e. contains no files (but
      may contain empty subdirectories)
    ~ means this is a directory where some of the files (possibly in
      subdirectories) have unknown size, and therefore the directory
      size may be underestimated (and average size inaccurate, as it
      is average of the files with known sizes).
    . means an error occurred while reading a subdirectory, and
      therefore the directory size may be underestimated (and average
      size inaccurate)
    ! means an error occurred while reading this directory

This an homage to the [ncdu tool](https://dev.yorhel.nl/ncdu) but for
rclone remotes.  It is missing lots of features at the moment
but is useful as it stands.

Note that it might take some time to delete big files/directories. The
UI won't respond in the meantime since the deletion is done synchronously.

For a non-interactive listing of the remote, see the
[tree](/commands/rclone_tree/) command. To just get the total size of
the remote you can also use the [size](/commands/rclone_size/) command.


```
rclone ncdu remote:path [flags]
```

## Options

```
  -h, --help   help for ncdu
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


---
title: "rclone rcd"
description: "Run rclone listening to remote control commands only."
slug: rclone_rcd
url: /commands/rclone_rcd/
versionIntroduced: v1.45
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/rcd/ and as part of making a release run "make commanddocs"
---
# rclone rcd

Run rclone listening to remote control commands only.

## Synopsis


This runs rclone so that it only listens to remote control commands.

This is useful if you are controlling rclone via the rc API.

If you pass in a path to a directory, rclone will serve that directory
for GET requests on the URL passed in.  It will also open the URL in
the browser when rclone is run.

See the [rc documentation](/rc/) for more info on the rc flags.

## Server options

Use `--addr` to specify which IP address and port the server should
listen on, eg `--addr *******:8000` or `--addr :8080` to listen to all
IPs.  By default it only listens on localhost.  You can use port
:0 to let the OS choose an available port.

If you set `--addr` to listen on a public or LAN accessible IP address
then using Authentication is advised - see the next section for info.

You can use a unix socket by setting the url to `unix:///path/to/socket`
or just by using an absolute path name. Note that unix sockets bypass the
authentication - this is expected to be done with file system permissions.

`--addr` may be repeated to listen on multiple IPs/ports/sockets.

`--server-read-timeout` and `--server-write-timeout` can be used to
control the timeouts on the server.  Note that this is the total time
for a transfer.

`--max-header-bytes` controls the maximum number of bytes the server will
accept in the HTTP header.

`--baseurl` controls the URL prefix that rclone serves from.  By default
rclone will serve from the root.  If you used `--baseurl "/rclone"` then
rclone would serve from a URL starting with "/rclone/".  This is
useful if you wish to proxy rclone serve.  Rclone automatically
inserts leading and trailing "/" on `--baseurl`, so `--baseurl "rclone"`,
`--baseurl "/rclone"` and `--baseurl "/rclone/"` are all treated
identically.

### TLS (SSL)

By default this will serve over http.  If you want you can serve over
https.  You will need to supply the `--cert` and `--key` flags.
If you wish to do client side certificate validation then you will need to
supply `--client-ca` also.

`--cert` should be a either a PEM encoded certificate or a concatenation
of that with the CA certificate.  `--key` should be the PEM encoded
private key and `--client-ca` should be the PEM encoded client
certificate authority certificate.

--min-tls-version is minimum TLS version that is acceptable. Valid
  values are "tls1.0", "tls1.1", "tls1.2" and "tls1.3" (default
  "tls1.0").

### Template

`--template` allows a user to specify a custom markup template for HTTP
and WebDAV serve functions.  The server exports the following markup
to be used within the template to server pages:

| Parameter   | Description |
| :---------- | :---------- |
| .Name       | The full path of a file/directory. |
| .Title      | Directory listing of .Name |
| .Sort       | The current sort used.  This is changeable via ?sort= parameter |
|             | Sort Options: namedirfirst,name,size,time (default namedirfirst) |
| .Order      | The current ordering used.  This is changeable via ?order= parameter |
|             | Order Options: asc,desc (default asc) |
| .Query      | Currently unused. |
| .Breadcrumb | Allows for creating a relative navigation |
|-- .Link     | The relative to the root link of the Text. |
|-- .Text     | The Name of the directory. |
| .Entries    | Information about a specific file/directory. |
|-- .URL      | The 'url' of an entry.  |
|-- .Leaf     | Currently same as 'URL' but intended to be 'just' the name. |
|-- .IsDir    | Boolean for if an entry is a directory or not. |
|-- .Size     | Size in Bytes of the entry. |
|-- .ModTime  | The UTC timestamp of an entry. |

### Authentication

By default this will serve files without needing a login.

You can either use an htpasswd file which can take lots of users, or
set a single username and password with the `--user` and `--pass` flags.

Use `--htpasswd /path/to/htpasswd` to provide an htpasswd file.  This is
in standard apache format and supports MD5, SHA1 and BCrypt for basic
authentication.  Bcrypt is recommended.

To create an htpasswd file:

    touch htpasswd
    htpasswd -B htpasswd user
    htpasswd -B htpasswd anotherUser

The password file can be updated while rclone is running.

Use `--realm` to set the authentication realm.

Use `--salt` to change the password hashing salt from the default.


```
rclone rcd <path to files to serve>* [flags]
```

## Options

```
  -h, --help   help for rcd
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


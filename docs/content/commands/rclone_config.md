---
title: "rclone config"
description: "Enter an interactive configuration session."
slug: rclone_config
url: /commands/rclone_config/
versionIntroduced: v1.39
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/config/ and as part of making a release run "make commanddocs"
---
# rclone config

Enter an interactive configuration session.

## Synopsis

Enter an interactive configuration session where you can setup new
remotes and manage existing ones. You may also set or remove a
password to protect your configuration.


```
rclone config [flags]
```

## Options

```
  -h, --help   help for config
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.
* [rclone config create](/commands/rclone_config_create/)	 - Create a new remote with name, type and options.
* [rclone config delete](/commands/rclone_config_delete/)	 - Delete an existing remote.
* [rclone config disconnect](/commands/rclone_config_disconnect/)	 - Disconnects user from remote
* [rclone config dump](/commands/rclone_config_dump/)	 - Dump the config file as JSON.
* [rclone config file](/commands/rclone_config_file/)	 - Show path of configuration file in use.
* [rclone config password](/commands/rclone_config_password/)	 - Update password in an existing remote.
* [rclone config paths](/commands/rclone_config_paths/)	 - Show paths used for configuration, cache, temp etc.
* [rclone config providers](/commands/rclone_config_providers/)	 - List in JSON format all the providers and options.
* [rclone config reconnect](/commands/rclone_config_reconnect/)	 - Re-authenticates user with remote.
* [rclone config show](/commands/rclone_config_show/)	 - Print (decrypted) config file, or the config for a single remote.
* [rclone config touch](/commands/rclone_config_touch/)	 - Ensure configuration file exists.
* [rclone config update](/commands/rclone_config_update/)	 - Update options in an existing remote.
* [rclone config userinfo](/commands/rclone_config_userinfo/)	 - Prints info about logged in user of remote.


---
title: "rclone checksum"
description: "Checks the files in the source against a SUM file."
slug: rclone_checksum
url: /commands/rclone_checksum/
versionIntroduced: v1.56
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/checksum/ and as part of making a release run "make commanddocs"
---
# rclone checksum

Checks the files in the source against a SUM file.

## Synopsis


Checks that hashsums of source files match the SUM file.
It compares hashes (MD5, SHA1, etc) and logs a report of files which
don't match.  It doesn't alter the file system.

If you supply the `--download` flag, it will download the data from remote
and calculate the contents hash on the fly.  This can be useful for remotes
that don't support hashes or if you really want to check all the data.

Note that hash values in the SUM file are treated as case insensitive.

If you supply the `--one-way` flag, it will only check that files in
the source match the files in the destination, not the other way
around. This means that extra files in the destination that are not in
the source will not be detected.

The `--differ`, `--missing-on-dst`, `--missing-on-src`, `--match`
and `--error` flags write paths, one per line, to the file name (or
stdout if it is `-`) supplied. What they write is described in the
help below. For example `--differ` will write all paths which are
present on both the source and destination but different.

The `--combined` flag will write a file (or stdout) which contains all
file paths with a symbol and then a space and then the path to tell
you what happened to it. These are reminiscent of diff files.

- `= path` means path was found in source and destination and was identical
- `- path` means path was missing on the source, so only in the destination
- `+ path` means path was missing on the destination, so only in the source
- `* path` means path was present in source and destination but different.
- `! path` means there was an error reading or hashing the source or dest.


```
rclone checksum <hash> sumfile src:path [flags]
```

## Options

```
      --combined string         Make a combined report of changes to this file
      --differ string           Report all non-matching files to this file
      --download                Check by hashing the contents
      --error string            Report all files with errors (hashing or reading) to this file
  -h, --help                    help for checksum
      --match string            Report all matching files to this file
      --missing-on-dst string   Report all files missing from the destination to this file
      --missing-on-src string   Report all files missing from the source to this file
      --one-way                 Check one way only, source files must exist on remote
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


---
title: "rclone config touch"
description: "Ensure configuration file exists."
slug: rclone_config_touch
url: /commands/rclone_config_touch/
versionIntroduced: v1.56
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/config/touch/ and as part of making a release run "make commanddocs"
---
# rclone config touch

Ensure configuration file exists.

```
rclone config touch [flags]
```

## Options

```
  -h, --help   help for touch
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone config](/commands/rclone_config/)	 - Enter an interactive configuration session.


---
title: "rclone completion bash"
description: "Generate the autocompletion script for bash"
slug: rclone_completion_bash
url: /commands/rclone_completion_bash/
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/completion/bash/ and as part of making a release run "make commanddocs"
---
# rclone completion bash

Generate the autocompletion script for bash

## Synopsis

Generate the autocompletion script for the bash shell.

This script depends on the 'bash-completion' package.
If it is not installed already, you can install it via your OS's package manager.

To load completions in your current shell session:

	source <(rclone completion bash)

To load completions for every new session, execute once:

### Linux:

	rclone completion bash > /etc/bash_completion.d/rclone

### macOS:

	rclone completion bash > $(brew --prefix)/etc/bash_completion.d/rclone

You will need to start a new shell for this setup to take effect.


```
rclone completion bash
```

## Options

```
  -h, --help              help for bash
      --no-descriptions   disable completion descriptions
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone completion](/commands/rclone_completion/)	 - Generate the autocompletion script for the specified shell


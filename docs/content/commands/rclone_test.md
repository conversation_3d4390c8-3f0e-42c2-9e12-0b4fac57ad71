---
title: "rclone test"
description: "Run a test command"
slug: rclone_test
url: /commands/rclone_test/
versionIntroduced: v1.55
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/test/ and as part of making a release run "make commanddocs"
---
# rclone test

Run a test command

## Synopsis

Rclone test is used to run test commands.

Select which test command you want with the subcommand, eg

    rclone test memory remote:

Each subcommand has its own options which you can see in their help.

**NB** Be careful running these commands, they may do strange things
so reading their documentation first is recommended.


## Options

```
  -h, --help   help for test
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.
* [rclone test changenotify](/commands/rclone_test_changenotify/)	 - Log any change notify requests for the remote passed in.
* [rclone test histogram](/commands/rclone_test_histogram/)	 - Makes a histogram of file name characters.
* [rclone test info](/commands/rclone_test_info/)	 - Discovers file name or other limitations for paths.
* [rclone test makefile](/commands/rclone_test_makefile/)	 - Make files with random contents of the size given
* [rclone test makefiles](/commands/rclone_test_makefiles/)	 - Make a random file hierarchy in a directory
* [rclone test memory](/commands/rclone_test_memory/)	 - Load all the objects at remote:path into memory and report memory stats.


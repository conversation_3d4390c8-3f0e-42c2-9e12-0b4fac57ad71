---
title: "rclone settier"
description: "Changes storage class/tier of objects in remote."
slug: rclone_settier
url: /commands/rclone_settier/
versionIntroduced: v1.44
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/settier/ and as part of making a release run "make commanddocs"
---
# rclone settier

Changes storage class/tier of objects in remote.

## Synopsis


rclone settier changes storage tier or class at remote if supported.
Few cloud storage services provides different storage classes on objects,
for example AWS S3 and Glacier, Azure Blob storage - Hot, Cool and Archive,
Google Cloud Storage, Regional Storage, Nearline, Coldline etc.

Note that, certain tier changes make objects not available to access immediately.
For example tiering to archive in azure blob storage makes objects in frozen state,
user can restore by setting tier to Hot/Cool, similarly S3 to Glacier makes object
inaccessible.true

You can use it to tier single object

    rclone settier Cool remote:path/file

Or use rclone filters to set tier on only specific files

	rclone --include "*.txt" settier Hot remote:path/dir

Or just provide remote directory and all files in directory will be tiered

    rclone settier tier remote:path/dir


```
rclone settier tier remote:path [flags]
```

## Options

```
  -h, --help   help for settier
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


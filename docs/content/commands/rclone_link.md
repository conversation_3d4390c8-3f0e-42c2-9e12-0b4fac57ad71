---
title: "rclone link"
description: "Generate public link to file/folder."
slug: rclone_link
url: /commands/rclone_link/
versionIntroduced: v1.41
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/link/ and as part of making a release run "make commanddocs"
---
# rclone link

Generate public link to file/folder.

## Synopsis

rclone link will create, retrieve or remove a public link to the given
file or folder.

    rclone link remote:path/to/file
    rclone link remote:path/to/folder/
    rclone link --unlink remote:path/to/folder/
    rclone link --expire 1d remote:path/to/file

If you supply the --expire flag, it will set the expiration time
otherwise it will use the default (100 years). **Note** not all
backends support the --expire flag - if the backend doesn't support it
then the link returned won't expire.

Use the --unlink flag to remove existing public links to the file or
folder. **Note** not all backends support "--unlink" flag - those that
don't will just ignore it.

If successful, the last line of the output will contain the
link. Exact capabilities depend on the remote, but the link will
always by default be created with the least constraints – e.g. no
expiry, no password protection, accessible without account.


```
rclone link remote:path [flags]
```

## Options

```
      --expire Duration   The amount of time that the link will be valid (default off)
  -h, --help              help for link
      --unlink            Remove existing public link to file/folder
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


---
title: "rclone rmdirs"
description: "Remove empty directories under the path."
slug: rclone_rmdirs
url: /commands/rclone_rmdirs/
versionIntroduced: v1.35
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/rmdirs/ and as part of making a release run "make commanddocs"
---
# rclone rmdirs

Remove empty directories under the path.

## Synopsis


This recursively removes any empty directories (including directories
that only contain empty directories), that it finds under the path.
The root path itself will also be removed if it is empty, unless
you supply the `--leave-root` flag.

Use command [rmdir](/commands/rclone_rmdir/) to delete just the empty
directory given by path, not recurse.

This is useful for tidying up remotes that rclone has left a lot of
empty directories in. For example the [delete](/commands/rclone_delete/)
command will delete files but leave the directory structure (unless
used with option `--rmdirs`).

To delete a path and any objects in it, use [purge](/commands/rclone_purge/)
command.


```
rclone rmdirs remote:path [flags]
```

## Options

```
  -h, --help         help for rmdirs
      --leave-root   Do not remove root directory if empty
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


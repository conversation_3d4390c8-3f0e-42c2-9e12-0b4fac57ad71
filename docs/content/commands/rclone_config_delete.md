---
title: "rclone config delete"
description: "Delete an existing remote."
slug: rclone_config_delete
url: /commands/rclone_config_delete/
versionIntroduced: v1.39
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/config/delete/ and as part of making a release run "make commanddocs"
---
# rclone config delete

Delete an existing remote.

```
rclone config delete name [flags]
```

## Options

```
  -h, --help   help for delete
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone config](/commands/rclone_config/)	 - Enter an interactive configuration session.


---
title: "rclone listremotes"
description: "List all the remotes in the config file."
slug: rclone_listremotes
url: /commands/rclone_listremotes/
versionIntroduced: v1.34
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/listremotes/ and as part of making a release run "make commanddocs"
---
# rclone listremotes

List all the remotes in the config file.

## Synopsis


rclone listremotes lists all the available remotes from the config file.

When used with the `--long` flag it lists the types too.


```
rclone listremotes [flags]
```

## Options

```
  -h, --help   help for listremotes
      --long   Show the type as well as names
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone](/commands/rclone/)	 - Show help for rclone commands, flags and backends.


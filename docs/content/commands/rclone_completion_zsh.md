---
title: "rclone completion zsh"
description: "Generate the autocompletion script for zsh"
slug: rclone_completion_zsh
url: /commands/rclone_completion_zsh/
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/completion/zsh/ and as part of making a release run "make commanddocs"
---
# rclone completion zsh

Generate the autocompletion script for zsh

## Synopsis

Generate the autocompletion script for the zsh shell.

If shell completion is not already enabled in your environment you will need
to enable it.  You can execute the following once:

	echo "autoload -U compinit; compinit" >> ~/.zshrc

To load completions in your current shell session:

	source <(rclone completion zsh); compdef _rclone rclone

To load completions for every new session, execute once:

### Linux:

	rclone completion zsh > "${fpath[1]}/_rclone"

### macOS:

	rclone completion zsh > $(brew --prefix)/share/zsh/site-functions/_rclone

You will need to start a new shell for this setup to take effect.


```
rclone completion zsh [flags]
```

## Options

```
  -h, --help              help for zsh
      --no-descriptions   disable completion descriptions
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone completion](/commands/rclone_completion/)	 - Generate the autocompletion script for the specified shell


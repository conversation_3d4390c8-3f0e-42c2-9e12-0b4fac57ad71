---
title: "rclone test makefile"
description: "Make files with random contents of the size given"
slug: rclone_test_makefile
url: /commands/rclone_test_makefile/
versionIntroduced: v1.59
# autogenerated - DO NOT EDIT, instead edit the source code in cmd/test/makefile/ and as part of making a release run "make commanddocs"
---
# rclone test makefile

Make files with random contents of the size given

```
rclone test makefile <size> [<file>]+ [flags]
```

## Options

```
      --ascii      Fill files with random ASCII printable bytes only
      --chargen    Fill files with a ASCII chargen pattern
  -h, --help       help for makefile
      --pattern    Fill files with a periodic pattern
      --seed int   Seed for the random number generator (0 for random) (default 1)
      --sparse     Make the files sparse (appear to be filled with ASCII 0x00)
      --zero       Fill files with ASCII 0x00
```

See the [global flags page](/flags/) for global options not listed here.

## SEE ALSO

* [rclone test](/commands/rclone_test/)	 - Run a test command


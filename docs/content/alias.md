---
title: "<PERSON><PERSON>"
description: "Remote Aliases"
versionIntroduced: "v1.40"
---

# {{< icon "fa fa-link" >}} <PERSON>as

The `alias` remote provides a new name for another remote.

Paths may be as deep as required or a local path, 
e.g. `remote:directory/subdirectory` or `/directory/subdirectory`.

During the initial setup with `rclone config` you will specify the target
remote. The target remote can either be a local path or another remote.

Subfolders can be used in target remote. Assume an alias remote named `backup`
with the target `mydrive:private/backup`. Invoking `rclone mkdir backup:desktop`
is exactly the same as invoking `rclone mkdir mydrive:private/backup/desktop`.

There will be no special handling of paths containing `..` segments.
Invoking `rclone mkdir backup:../desktop` is exactly the same as invoking
`rclone mkdir mydrive:private/backup/../desktop`.
The empty path is not allowed as a remote. To alias the current directory
use `.` instead.

The target remote can also be a [connection string](/docs/#connection-strings). 
This can be used to modify the config of a remote for different uses, e.g.
the alias  `myDriveTrash` with the target remote `myDrive,trashed_only:` 
can be used to only show the trashed files in `myDrive`.

## Configuration

Here is an example of how to make an alias called `remote` for local folder.
First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> remote
Type of storage to configure.
Choose a number from below, or type in your own value
[snip]
XX / Alias for an existing remote
   \ "alias"
[snip]
Storage> alias
Remote or path to alias.
Can be "myremote:path/to/dir", "myremote:bucket", "myremote:" or "/local/path".
remote> /mnt/storage/backup
Remote config
--------------------
[remote]
remote = /mnt/storage/backup
--------------------
y) Yes this is OK
e) Edit this remote
d) Delete this remote
y/e/d> y
Current remotes:

Name                 Type
====                 ====
remote               alias

e) Edit existing remote
n) New remote
d) Delete remote
r) Rename remote
c) Copy remote
s) Set configuration password
q) Quit config
e/n/d/r/c/s/q> q
```

Once configured you can then use `rclone` like this,

List directories in top level in `/mnt/storage/backup`

    rclone lsd remote:

List all the files in `/mnt/storage/backup`

    rclone ls remote:

Copy another local directory to the alias directory called source

    rclone copy /home/<USER>

{{< rem autogenerated options start" - DO NOT EDIT - instead edit fs.RegInfo in backend/alias/alias.go then run make backenddocs" >}}
### Standard options

Here are the Standard options specific to alias (Alias for an existing remote).

#### --alias-remote

Remote or path to alias.

Can be "myremote:path/to/dir", "myremote:bucket", "myremote:" or "/local/path".

Properties:

- Config:      remote
- Env Var:     RCLONE_ALIAS_REMOTE
- Type:        string
- Required:    true

{{< rem autogenerated options stop >}}

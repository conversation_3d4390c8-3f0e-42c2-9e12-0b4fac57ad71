---
title: "Koofr"
description: "Rclone docs for Koofr"
versionIntroduced: "v1.47"
---

# {{< icon "fa fa-suitcase" >}} Koofr

Paths are specified as `remote:path`

Paths may be as deep as required, e.g. `remote:directory/subdirectory`.

## Configuration

The initial setup for <PERSON>of<PERSON> involves creating an application password for
rclone. You can do that by opening the Koofr
[web application](https://app.koofr.net/app/admin/preferences/password),
giving the password a nice name like `rclone` and clicking on generate.

Here is an example of how to make a remote called `koofr`.  First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> koofr
Option Storage.
Type of storage to configure.
Choose a number from below, or type in your own value.
[snip]
22 / Koofr, Digi Storage and other Koofr-compatible storage providers
   \ (koofr)
[snip]
Storage> koofr
Option provider.
Choose your storage provider.
Choose a number from below, or type in your own value.
Press Enter to leave empty.
 1 / Koofr, https://app.koofr.net/
   \ (koofr)
 2 / Digi Storage, https://storage.rcs-rds.ro/
   \ (digistorage)
 3 / Any other Koofr API compatible storage service
   \ (other)
provider> 1    
Option user.
Your user name.
Enter a value.
user> USERNAME
Option password.
Your password for rclone (generate one at https://app.koofr.net/app/admin/preferences/password).
Choose an alternative below.
y) Yes, type in my own password
g) Generate random password
y/g> y
Enter the password:
password:
Confirm the password:
password:
Edit advanced config?
y) Yes
n) No (default)
y/n> n
Remote config
--------------------
[koofr]
type = koofr
provider = koofr
user = USERNAME
password = *** ENCRYPTED ***
--------------------
y) Yes this is OK (default)
e) Edit this remote
d) Delete this remote
y/e/d> y
```

You can choose to edit advanced config in order to enter your own service URL
if you use an on-premise or white label Koofr instance, or choose an alternative
mount instead of your primary storage.

Once configured you can then use `rclone` like this,

List directories in top level of your Koofr

    rclone lsd koofr:

List all the files in your Koofr

    rclone ls koofr:

To copy a local directory to an Koofr directory called backup

    rclone copy /home/<USER>

### Restricted filename characters

In addition to the [default restricted characters set](/overview/#restricted-characters)
the following characters are also replaced:

| Character | Value | Replacement |
| --------- |:-----:|:-----------:|
| \         | 0x5C  | ＼           |

Invalid UTF-8 bytes will also be [replaced](/overview/#invalid-utf8),
as they can't be used in XML strings.

{{< rem autogenerated options start" - DO NOT EDIT - instead edit fs.RegInfo in backend/koofr/koofr.go then run make backenddocs" >}}
### Standard options

Here are the Standard options specific to koofr (Koofr, Digi Storage and other Koofr-compatible storage providers).

#### --koofr-provider

Choose your storage provider.

Properties:

- Config:      provider
- Env Var:     RCLONE_KOOFR_PROVIDER
- Type:        string
- Required:    false
- Examples:
    - "koofr"
        - Koofr, https://app.koofr.net/
    - "digistorage"
        - Digi Storage, https://storage.rcs-rds.ro/
    - "other"
        - Any other Koofr API compatible storage service

#### --koofr-endpoint

The Koofr API endpoint to use.

Properties:

- Config:      endpoint
- Env Var:     RCLONE_KOOFR_ENDPOINT
- Provider:    other
- Type:        string
- Required:    true

#### --koofr-user

Your user name.

Properties:

- Config:      user
- Env Var:     RCLONE_KOOFR_USER
- Type:        string
- Required:    true

#### --koofr-password

Your password for rclone (generate one at https://app.koofr.net/app/admin/preferences/password).

**NB** Input to this must be obscured - see [rclone obscure](/commands/rclone_obscure/).

Properties:

- Config:      password
- Env Var:     RCLONE_KOOFR_PASSWORD
- Provider:    koofr
- Type:        string
- Required:    true

#### --koofr-password

Your password for rclone (generate one at https://storage.rcs-rds.ro/app/admin/preferences/password).

**NB** Input to this must be obscured - see [rclone obscure](/commands/rclone_obscure/).

Properties:

- Config:      password
- Env Var:     RCLONE_KOOFR_PASSWORD
- Provider:    digistorage
- Type:        string
- Required:    true

#### --koofr-password

Your password for rclone (generate one at your service's settings page).

**NB** Input to this must be obscured - see [rclone obscure](/commands/rclone_obscure/).

Properties:

- Config:      password
- Env Var:     RCLONE_KOOFR_PASSWORD
- Provider:    other
- Type:        string
- Required:    true

### Advanced options

Here are the Advanced options specific to koofr (Koofr, Digi Storage and other Koofr-compatible storage providers).

#### --koofr-mountid

Mount ID of the mount to use.

If omitted, the primary mount is used.

Properties:

- Config:      mountid
- Env Var:     RCLONE_KOOFR_MOUNTID
- Type:        string
- Required:    false

#### --koofr-setmtime

Does the backend support setting modification time.

Set this to false if you use a mount ID that points to a Dropbox or Amazon Drive backend.

Properties:

- Config:      setmtime
- Env Var:     RCLONE_KOOFR_SETMTIME
- Type:        bool
- Default:     true

#### --koofr-encoding

The encoding for the backend.

See the [encoding section in the overview](/overview/#encoding) for more info.

Properties:

- Config:      encoding
- Env Var:     RCLONE_KOOFR_ENCODING
- Type:        MultiEncoder
- Default:     Slash,BackSlash,Del,Ctl,InvalidUtf8,Dot

{{< rem autogenerated options stop >}}

## Limitations

Note that Koofr is case insensitive so you can't have a file called
"Hello.doc" and one called "hello.doc".

## Providers

### Koofr

This is the original [Koofr](https://koofr.eu) storage provider used as main example and described in the [configuration](#configuration) section above.

### Digi Storage 

[Digi Storage](https://www.digi.ro/servicii/online/digi-storage) is a cloud storage service run by [Digi.ro](https://www.digi.ro/) that
provides a Koofr API.

Here is an example of how to make a remote called `ds`.  First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> ds
Option Storage.
Type of storage to configure.
Choose a number from below, or type in your own value.
[snip]
22 / Koofr, Digi Storage and other Koofr-compatible storage providers
   \ (koofr)
[snip]
Storage> koofr
Option provider.
Choose your storage provider.
Choose a number from below, or type in your own value.
Press Enter to leave empty.
 1 / Koofr, https://app.koofr.net/
   \ (koofr)
 2 / Digi Storage, https://storage.rcs-rds.ro/
   \ (digistorage)
 3 / Any other Koofr API compatible storage service
   \ (other)
provider> 2
Option user.
Your user name.
Enter a value.
user> USERNAME
Option password.
Your password for rclone (generate one at https://storage.rcs-rds.ro/app/admin/preferences/password).
Choose an alternative below.
y) Yes, type in my own password
g) Generate random password
y/g> y
Enter the password:
password:
Confirm the password:
password:
Edit advanced config?
y) Yes
n) No (default)
y/n> n
--------------------
[ds]
type = koofr
provider = digistorage
user = USERNAME
password = *** ENCRYPTED ***
--------------------
y) Yes this is OK (default)
e) Edit this remote
d) Delete this remote
y/e/d> y
```

### Other

You may also want to use another, public or private storage provider that runs a Koofr API compatible service, by simply providing the base URL to connect to.

Here is an example of how to make a remote called `other`.  First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> other
Option Storage.
Type of storage to configure.
Choose a number from below, or type in your own value.
[snip]
22 / Koofr, Digi Storage and other Koofr-compatible storage providers
   \ (koofr)
[snip]
Storage> koofr
Option provider.
Choose your storage provider.
Choose a number from below, or type in your own value.
Press Enter to leave empty.
 1 / Koofr, https://app.koofr.net/
   \ (koofr)
 2 / Digi Storage, https://storage.rcs-rds.ro/
   \ (digistorage)
 3 / Any other Koofr API compatible storage service
   \ (other)
provider> 3
Option endpoint.
The Koofr API endpoint to use.
Enter a value.
endpoint> https://koofr.other.org
Option user.
Your user name.
Enter a value.
user> USERNAME
Option password.
Your password for rclone (generate one at your service's settings page).
Choose an alternative below.
y) Yes, type in my own password
g) Generate random password
y/g> y
Enter the password:
password:
Confirm the password:
password:
Edit advanced config?
y) Yes
n) No (default)
y/n> n
--------------------
[other]
type = koofr
provider = other
endpoint = https://koofr.other.org
user = USERNAME
password = *** ENCRYPTED ***
--------------------
y) Yes this is OK (default)
e) Edit this remote
d) Delete this remote
y/e/d> y
```

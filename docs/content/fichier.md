---
title: "1Fichier"
description: "Rclone docs for 1Fichier"
versionIntroduced: "v1.49"
---

# {{< icon "fa fa-archive" >}} 1Fichier

This is a backend for the [1fichier](https://1fichier.com) cloud
storage service. Note that a Premium subscription is required to use
the API.

Paths are specified as `remote:path`

Paths may be as deep as required, e.g. `remote:directory/subdirectory`.

## Configuration

The initial setup for 1Fichi<PERSON> involves getting the API key from the website which you
need to do in your browser.

Here is an example of how to make a remote called `remote`.  First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> remote
Type of storage to configure.
Enter a string value. Press Enter for the default ("").
Choose a number from below, or type in your own value
[snip]
XX / 1Fichier
   \ "fichier"
[snip]
Storage> fichier
** See help for fichier backend at: https://rclone.org/fichier/ **

Your API Key, get it from https://1fichier.com/console/params.pl
Enter a string value. Press Enter for the default ("").
api_key> example_key

Edit advanced config? (y/n)
y) Yes
n) No
y/n> 
Remote config
--------------------
[remote]
type = fichier
api_key = example_key
--------------------
y) Yes this is OK
e) Edit this remote
d) Delete this remote
y/e/d> y
```

Once configured you can then use `rclone` like this,

List directories in top level of your 1Fichier account

    rclone lsd remote:

List all the files in your 1Fichier account

    rclone ls remote:

To copy a local directory to a 1Fichier directory called backup

    rclone copy /home/<USER>

### Modified time and hashes ###

1Fichier does not support modification times. It supports the Whirlpool hash algorithm.

### Duplicated files ###

1Fichier can have two files with exactly the same name and path (unlike a
normal file system).

Duplicated files cause problems with the syncing and you will see
messages in the log about duplicates.

### Restricted filename characters

In addition to the [default restricted characters set](/overview/#restricted-characters)
the following characters are also replaced:

| Character | Value | Replacement |
| --------- |:-----:|:-----------:|
| \         | 0x5C  | ＼           |
| <         | 0x3C  | ＜           |
| >         | 0x3E  | ＞           |
| "         | 0x22  | ＂           |
| $         | 0x24  | ＄           |
| `         | 0x60  | ｀           |
| '         | 0x27  | ＇           |

File names can also not start or end with the following characters.
These only get replaced if they are the first or last character in the
name:

| Character | Value | Replacement |
| --------- |:-----:|:-----------:|
| SP        | 0x20  | ␠           |

Invalid UTF-8 bytes will also be [replaced](/overview/#invalid-utf8),
as they can't be used in JSON strings.

{{< rem autogenerated options start" - DO NOT EDIT - instead edit fs.RegInfo in backend/fichier/fichier.go then run make backenddocs" >}}
### Standard options

Here are the Standard options specific to fichier (1Fichier).

#### --fichier-api-key

Your API Key, get it from https://1fichier.com/console/params.pl.

Properties:

- Config:      api_key
- Env Var:     RCLONE_FICHIER_API_KEY
- Type:        string
- Required:    false

### Advanced options

Here are the Advanced options specific to fichier (1Fichier).

#### --fichier-shared-folder

If you want to download a shared folder, add this parameter.

Properties:

- Config:      shared_folder
- Env Var:     RCLONE_FICHIER_SHARED_FOLDER
- Type:        string
- Required:    false

#### --fichier-file-password

If you want to download a shared file that is password protected, add this parameter.

**NB** Input to this must be obscured - see [rclone obscure](/commands/rclone_obscure/).

Properties:

- Config:      file_password
- Env Var:     RCLONE_FICHIER_FILE_PASSWORD
- Type:        string
- Required:    false

#### --fichier-folder-password

If you want to list the files in a shared folder that is password protected, add this parameter.

**NB** Input to this must be obscured - see [rclone obscure](/commands/rclone_obscure/).

Properties:

- Config:      folder_password
- Env Var:     RCLONE_FICHIER_FOLDER_PASSWORD
- Type:        string
- Required:    false

#### --fichier-encoding

The encoding for the backend.

See the [encoding section in the overview](/overview/#encoding) for more info.

Properties:

- Config:      encoding
- Env Var:     RCLONE_FICHIER_ENCODING
- Type:        MultiEncoder
- Default:     Slash,LtGt,DoubleQuote,SingleQuote,BackQuote,Dollar,BackSlash,Del,Ctl,LeftSpace,RightSpace,InvalidUtf8,Dot

{{< rem autogenerated options stop >}}

## Limitations

`rclone about` is not supported by the 1Fichier backend. Backends without
this capability cannot determine free space for an rclone mount or
use policy `mfs` (most free space) as a member of an rclone union
remote.

See [List of backends that do not support rclone about](https://rclone.org/overview/#optional-features) and [rclone about](https://rclone.org/commands/rclone_about/)


---
title: "pCloud"
description: "Rclone docs for pCloud"
versionIntroduced: "v1.39"
---

# {{< icon "fa fa-cloud" >}} pCloud

Paths are specified as `remote:path`

Paths may be as deep as required, e.g. `remote:directory/subdirectory`.

## Configuration

The initial setup for pCloud involves getting a token from pCloud which you
need to do in your browser.  `rclone config` walks you through it.

Here is an example of how to make a remote called `remote`.  First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> remote
Type of storage to configure.
Choose a number from below, or type in your own value
[snip]
XX / Pcloud
   \ "pcloud"
[snip]
Storage> pcloud
Pcloud App Client Id - leave blank normally.
client_id> 
Pcloud App Client Secret - leave blank normally.
client_secret> 
Remote config
Use web browser to automatically authenticate rclone with remote?
 * Say Y if the machine running rclone has a web browser you can use
 * Say N if running rclone on a (remote) machine without web browser access
If not sure try Y. If Y failed, try N.
y) Yes
n) No
y/n> y
If your browser doesn't open automatically go to the following link: http://127.0.0.1:53682/auth
Log in and authorize rclone for access
Waiting for code...
Got code
--------------------
[remote]
client_id = 
client_secret = 
token = {"access_token":"XXX","token_type":"bearer","expiry":"0001-01-01T00:00:00Z"}
--------------------
y) Yes this is OK
e) Edit this remote
d) Delete this remote
y/e/d> y
```

See the [remote setup docs](/remote_setup/) for how to set it up on a
machine with no Internet browser available.

Note that rclone runs a webserver on your local machine to collect the
token as returned from pCloud. This only runs from the moment it opens
your browser to the moment you get back the verification code.  This
is on `http://127.0.0.1:53682/` and this it may require you to unblock
it temporarily if you are running a host firewall.

Once configured you can then use `rclone` like this,

List directories in top level of your pCloud

    rclone lsd remote:

List all the files in your pCloud

    rclone ls remote:

To copy a local directory to a pCloud directory called backup

    rclone copy /home/<USER>

### Modified time and hashes ###

pCloud allows modification times to be set on objects accurate to 1
second.  These will be used to detect whether objects need syncing or
not.  In order to set a Modification time pCloud requires the object
be re-uploaded.

pCloud supports MD5 and SHA1 hashes in the US region, and SHA1 and SHA256
hashes in the EU region, so you can use the `--checksum` flag.

### Restricted filename characters

In addition to the [default restricted characters set](/overview/#restricted-characters)
the following characters are also replaced:

| Character | Value | Replacement |
| --------- |:-----:|:-----------:|
| \         | 0x5C  | ＼          |

Invalid UTF-8 bytes will also be [replaced](/overview/#invalid-utf8),
as they can't be used in JSON strings.

### Deleting files

Deleted files will be moved to the trash.  Your subscription level
will determine how long items stay in the trash.  `rclone cleanup` can
be used to empty the trash.

### Emptying the trash

Due to an API limitation, the `rclone cleanup` command will only work if you 
set your username and password in the advanced options for this backend. 
Since we generally want to avoid storing user passwords in the rclone config
file, we advise you to only set this up if you need the `rclone cleanup` command to work.

### Root folder ID

You can set the `root_folder_id` for rclone.  This is the directory
(identified by its `Folder ID`) that rclone considers to be the root
of your pCloud drive.

Normally you will leave this blank and rclone will determine the
correct root to use itself.

However you can set this to restrict rclone to a specific folder
hierarchy.

In order to do this you will have to find the `Folder ID` of the
directory you wish rclone to display.  This will be the `folder` field
of the URL when you open the relevant folder in the pCloud web
interface.

So if the folder you want rclone to use has a URL which looks like
`https://my.pcloud.com/#page=filemanager&folder=5xxxxxxxx8&tpl=foldergrid`
in the browser, then you use `5xxxxxxxx8` as
the `root_folder_id` in the config.

{{< rem autogenerated options start" - DO NOT EDIT - instead edit fs.RegInfo in backend/pcloud/pcloud.go then run make backenddocs" >}}
### Standard options

Here are the Standard options specific to pcloud (Pcloud).

#### --pcloud-client-id

OAuth Client Id.

Leave blank normally.

Properties:

- Config:      client_id
- Env Var:     RCLONE_PCLOUD_CLIENT_ID
- Type:        string
- Required:    false

#### --pcloud-client-secret

OAuth Client Secret.

Leave blank normally.

Properties:

- Config:      client_secret
- Env Var:     RCLONE_PCLOUD_CLIENT_SECRET
- Type:        string
- Required:    false

### Advanced options

Here are the Advanced options specific to pcloud (Pcloud).

#### --pcloud-token

OAuth Access Token as a JSON blob.

Properties:

- Config:      token
- Env Var:     RCLONE_PCLOUD_TOKEN
- Type:        string
- Required:    false

#### --pcloud-auth-url

Auth server URL.

Leave blank to use the provider defaults.

Properties:

- Config:      auth_url
- Env Var:     RCLONE_PCLOUD_AUTH_URL
- Type:        string
- Required:    false

#### --pcloud-token-url

Token server url.

Leave blank to use the provider defaults.

Properties:

- Config:      token_url
- Env Var:     RCLONE_PCLOUD_TOKEN_URL
- Type:        string
- Required:    false

#### --pcloud-encoding

The encoding for the backend.

See the [encoding section in the overview](/overview/#encoding) for more info.

Properties:

- Config:      encoding
- Env Var:     RCLONE_PCLOUD_ENCODING
- Type:        MultiEncoder
- Default:     Slash,BackSlash,Del,Ctl,InvalidUtf8,Dot

#### --pcloud-root-folder-id

Fill in for rclone to use a non root folder as its starting point.

Properties:

- Config:      root_folder_id
- Env Var:     RCLONE_PCLOUD_ROOT_FOLDER_ID
- Type:        string
- Default:     "d0"

#### --pcloud-hostname

Hostname to connect to.

This is normally set when rclone initially does the oauth connection,
however you will need to set it by hand if you are using remote config
with rclone authorize.


Properties:

- Config:      hostname
- Env Var:     RCLONE_PCLOUD_HOSTNAME
- Type:        string
- Default:     "api.pcloud.com"
- Examples:
    - "api.pcloud.com"
        - Original/US region
    - "eapi.pcloud.com"
        - EU region

#### --pcloud-username

Your pcloud username.
			
This is only required when you want to use the cleanup command. Due to a bug
in the pcloud API the required API does not support OAuth authentication so
we have to rely on user password authentication for it.

Properties:

- Config:      username
- Env Var:     RCLONE_PCLOUD_USERNAME
- Type:        string
- Required:    false

#### --pcloud-password

Your pcloud password.

**NB** Input to this must be obscured - see [rclone obscure](/commands/rclone_obscure/).

Properties:

- Config:      password
- Env Var:     RCLONE_PCLOUD_PASSWORD
- Type:        string
- Required:    false

{{< rem autogenerated options stop >}}

---
title: "premiumize.me"
description: "Rclone docs for premiumize.me"
versionIntroduced: "v1.49"
---

# {{< icon "fa fa-user" >}} premiumize.me

Paths are specified as `remote:path`

Paths may be as deep as required, e.g. `remote:directory/subdirectory`.

## Configuration

The initial setup for [premiumize.me](https://premiumize.me/) involves getting a token from premiumize.me which you
need to do in your browser.  `rclone config` walks you through it.

Here is an example of how to make a remote called `remote`.  First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> remote
Type of storage to configure.
Enter a string value. Press Enter for the default ("").
Choose a number from below, or type in your own value
[snip]
XX / premiumize.me
   \ "premiumizeme"
[snip]
Storage> premiumizeme
** See help for premiumizeme backend at: https://rclone.org/premiumizeme/ **

Remote config
Use web browser to automatically authenticate rclone with remote?
 * Say Y if the machine running rclone has a web browser you can use
 * Say N if running rclone on a (remote) machine without web browser access
If not sure try Y. If Y failed, try N.
y) Yes
n) No
y/n> y
If your browser doesn't open automatically go to the following link: http://127.0.0.1:53682/auth
Log in and authorize rclone for access
Waiting for code...
Got code
--------------------
[remote]
type = premiumizeme
token = {"access_token":"XXX","token_type":"Bearer","refresh_token":"XXX","expiry":"2029-08-07T18:44:15.548915378+01:00"}
--------------------
y) Yes this is OK
e) Edit this remote
d) Delete this remote
y/e/d> 
```

See the [remote setup docs](/remote_setup/) for how to set it up on a
machine with no Internet browser available.

Note that rclone runs a webserver on your local machine to collect the
token as returned from premiumize.me. This only runs from the moment it opens
your browser to the moment you get back the verification code.  This
is on `http://127.0.0.1:53682/` and this it may require you to unblock
it temporarily if you are running a host firewall.

Once configured you can then use `rclone` like this,

List directories in top level of your premiumize.me

    rclone lsd remote:

List all the files in your premiumize.me

    rclone ls remote:

To copy a local directory to an premiumize.me directory called backup

    rclone copy /home/<USER>

### Modified time and hashes

premiumize.me does not support modification times or hashes, therefore
syncing will default to `--size-only` checking.  Note that using
`--update` will work.

### Restricted filename characters

In addition to the [default restricted characters set](/overview/#restricted-characters)
the following characters are also replaced:

| Character | Value | Replacement |
| --------- |:-----:|:-----------:|
| \         | 0x5C  | ＼           |
| "         | 0x22  | ＂           |

Invalid UTF-8 bytes will also be [replaced](/overview/#invalid-utf8),
as they can't be used in JSON strings.

{{< rem autogenerated options start" - DO NOT EDIT - instead edit fs.RegInfo in backend/premiumizeme/premiumizeme.go then run make backenddocs" >}}
### Standard options

Here are the Standard options specific to premiumizeme (premiumize.me).

#### --premiumizeme-api-key

API Key.

This is not normally used - use oauth instead.


Properties:

- Config:      api_key
- Env Var:     RCLONE_PREMIUMIZEME_API_KEY
- Type:        string
- Required:    false

### Advanced options

Here are the Advanced options specific to premiumizeme (premiumize.me).

#### --premiumizeme-encoding

The encoding for the backend.

See the [encoding section in the overview](/overview/#encoding) for more info.

Properties:

- Config:      encoding
- Env Var:     RCLONE_PREMIUMIZEME_ENCODING
- Type:        MultiEncoder
- Default:     Slash,DoubleQuote,BackSlash,Del,Ctl,InvalidUtf8,Dot

{{< rem autogenerated options stop >}}

## Limitations

Note that premiumize.me is case insensitive so you can't have a file called
"Hello.doc" and one called "hello.doc".

premiumize.me file names can't have the `\` or `"` characters in.
rclone maps these to and from an identical looking unicode equivalents
`＼` and `＂`

premiumize.me only supports filenames up to 255 characters in length.

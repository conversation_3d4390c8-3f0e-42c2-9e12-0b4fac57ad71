---
title: "put.io"
description: "Rclone docs for put.io"
versionIntroduced: "v1.49"
---

# {{< icon "fas fa-parking" >}} put.io

Paths are specified as `remote:path`

put.io paths may be as deep as required, e.g.
`remote:directory/subdirectory`.

## Configuration

The initial setup for put.io involves getting a token from put.io
which you need to do in your browser.  `rclone config` walks you
through it.

Here is an example of how to make a remote called `remote`.  First run:

     rclone config

This will guide you through an interactive setup process:

```
No remotes found, make a new one?
n) New remote
s) Set configuration password
q) Quit config
n/s/q> n
name> putio
Type of storage to configure.
Enter a string value. Press Enter for the default ("").
Choose a number from below, or type in your own value
[snip]
XX / Put.io
   \ "putio"
[snip]
Storage> putio
** See help for putio backend at: https://rclone.org/putio/ **

Remote config
Use web browser to automatically authenticate rclone with remote?
 * Say Y if the machine running rclone has a web browser you can use
 * Say N if running rclone on a (remote) machine without web browser access
If not sure try Y. If Y failed, try N.
y) Yes
n) No
y/n> y
If your browser doesn't open automatically go to the following link: http://127.0.0.1:53682/auth
Log in and authorize rclone for access
Waiting for code...
Got code
--------------------
[putio]
type = putio
token = {"access_token":"XXXXXXXX","expiry":"0001-01-01T00:00:00Z"}
--------------------
y) Yes this is OK
e) Edit this remote
d) Delete this remote
y/e/d> y
Current remotes:

Name                 Type
====                 ====
putio                putio

e) Edit existing remote
n) New remote
d) Delete remote
r) Rename remote
c) Copy remote
s) Set configuration password
q) Quit config
e/n/d/r/c/s/q> q
```

See the [remote setup docs](/remote_setup/) for how to set it up on a
machine with no Internet browser available.

Note that rclone runs a webserver on your local machine to collect the
token as returned from put.io  if using web browser to automatically 
authenticate. This only
runs from the moment it opens your browser to the moment you get back
the verification code.  This is on `http://127.0.0.1:53682/` and this
it may require you to unblock it temporarily if you are running a host
firewall, or use manual mode.

You can then use it like this,

List directories in top level of your put.io

    rclone lsd remote:

List all the files in your put.io

    rclone ls remote:

To copy a local directory to a put.io directory called backup

    rclone copy /home/<USER>

### Restricted filename characters

In addition to the [default restricted characters set](/overview/#restricted-characters)
the following characters are also replaced:

| Character | Value | Replacement |
| --------- |:-----:|:-----------:|
| \         | 0x5C  | ＼           |

Invalid UTF-8 bytes will also be [replaced](/overview/#invalid-utf8),
as they can't be used in JSON strings.

{{< rem autogenerated options start" - DO NOT EDIT - instead edit fs.RegInfo in backend/putio/putio.go then run make backenddocs" >}}
### Advanced options

Here are the Advanced options specific to putio (Put.io).

#### --putio-encoding

The encoding for the backend.

See the [encoding section in the overview](/overview/#encoding) for more info.

Properties:

- Config:      encoding
- Env Var:     RCLONE_PUTIO_ENCODING
- Type:        MultiEncoder
- Default:     Slash,BackSlash,Del,Ctl,InvalidUtf8,Dot

{{< rem autogenerated options stop >}}

## Limitations

put.io has rate limiting. When you hit a limit, rclone automatically
retries after waiting the amount of time requested by the server.

If you want to avoid ever hitting these limits, you may use the
`--tpslimit` flag with a low number. Note that the imposed limits
may be different for different operations, and may change over time.

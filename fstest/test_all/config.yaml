tests:
 - path: backend
   addbackend: true
   nobinary:   true
   short:      true
 - path: fs/operations
   fastlist: true
 - path: fs/sync
   fastlist: true
 - path: vfs
 - path: cmd/serve/restic
   localonly: true
backends:
 # - backend:  "amazonclouddrive"
 #   remote:   "TestAmazonCloudDrive:"
 #   fastlist: false
 - backend:  "local"
   remote:   ""
   fastlist: false
 - backend:  "b2"
   remote:   "TestB2:"
   fastlist: true
   listretries: 5
 - backend:  "crypt"
   remote:   "TestCryptDrive:"
   fastlist: true
   ignore:
     - TestCopyFileMaxTransfer
 - backend:  "crypt"
   remote:   "TestCryptSwift:"
   fastlist: false
 ## chunker
 - backend:  "chunker"
   remote:   "TestChunkerLocal:"
   fastlist: true
 - backend:  "chunker"
   remote:   "TestChunkerNometaLocal:"
   fastlist: true
 - backend:  "chunker"
   remote:   "TestChunkerChunk3bLocal:"
   fastlist: true
   maxfile:  6k
 - backend:  "chunker"
   remote:   "TestChunkerChunk3bNometaLocal:"
   fastlist: true
   maxfile:  6k
 - backend:  "chunker"
   remote:   "TestChunkerChunk3bNoRenameLocal:"
   fastlist: true
   maxfile:  6k
 - backend:  "chunker"
   remote:   "TestChunkerMailru:"
   fastlist: true
 - backend:  "chunker"
   remote:   "TestChunkerChunk50bMailru:"
   fastlist: true
   maxfile:  10k
 - backend:  "chunker"
   remote:   "TestChunkerChunk50bYandex:"
   fastlist: true
   maxfile:  1k
 - backend:  "chunker"
   remote:   "TestChunkerChunk50bBox:"
   fastlist: true
   maxfile:  1k
 - backend:  "chunker"
   remote:   "TestChunkerS3:"
   fastlist: true
 - backend:  "chunker"
   remote:   "TestChunkerChunk50bS3:"
   fastlist: true
   maxfile:  1k
 - backend:  "chunker"
   remote:   "TestChunkerChunk50bMD5HashS3:"
   fastlist: true
   maxfile:  1k
 - backend:  "chunker"
   remote:   "TestChunkerChunk50bSHA1HashS3:"
   fastlist: true
   maxfile:  1k
 - backend:  "chunker"
   remote:   "TestChunkerOverCrypt:"
   fastlist: true
   maxfile:  6k
 - backend:  "chunker"
   remote:   "TestChunkerChunk50bMD5QuickS3:"
   fastlist: true
   maxfile:  1k
 - backend:  "chunker"
   remote:   "TestChunkerChunk50bSHA1QuickS3:"
   fastlist: true
   maxfile:  1k
 ## end chunker
 - backend:  "combine"
   remote:   "TestCombine:dir1"
   fastlist: false
 ## begin compress
 - backend:  "compress"
   remote:   "TestCompress:"
   fastlist: false
 - backend:  "compress"
   remote:   "TestCompressSwift:"
   fastlist: false
 - backend:  "compress"
   remote:   "TestCompressDrive:"
   fastlist: false
 - backend:  "compress"
   remote:   "TestCompressS3:"
   fastlist: false
## end compress
 - backend:  "drive"
   remote:   "TestDrive:"
   fastlist: true
   ignore:
     # Test with CutoffModeHard does not result in ErrorMaxTransferLimitReachedFatal
     # because googleapi replaces it with a non-fatal error
     - TestCopyFileMaxTransfer
 - backend:  "dropbox"
   remote:   "TestDropbox:"
   fastlist: false
   ignore:
     # This test doesn't work on a standard dropbox account because it
     # tries to set the expiry of the link
     - TestIntegration/FsMkdir/FsPutFiles/PublicLink
 - backend:  "filefabric"
   remote:   "TestFileFabric:"
   fastlist: false
   extratime: 2.0
 - backend:  "googlecloudstorage"
   remote:   "TestGoogleCloudStorage:"
   fastlist: true
 - backend:  "googlecloudstorage"
   remote:   "TestGoogleCloudStorage,directory_markers:"
   fastlist: true
 - backend:  "googlephotos"
   remote:   "TestGooglePhotos:"
   tests:
     - backend
 - backend: "hidrive"
   remote:   "TestHiDrive:"
   fastlist: false
 - backend:  "internetarchive"
   remote:   "TestIA:rclone-integration-test"
   fastlist: true
   tests:
     - backend
   extratime: 2.0
 - backend:  "jottacloud"
   remote:   "TestJottacloud:"
   fastlist: true
 - backend:  "memory"
   remote:   ":memory:"
   fastlist: true
 - backend:  "netstorage"
   remote:   "TestnStorage:"
   fastlist: true
 - backend:  "onedrive"
   remote:   "TestOneDrive:"
   fastlist: false
   ignore:
     # This test doesn't work on a standard Onedrive account returning
     # accessDenied: accountUpgradeRequired: Account Upgrade is required for this operation.
     - TestIntegration/FsMkdir/FsPutFiles/PublicLink
 - backend:  "onedrive"
   remote:   "TestOneDriveBusiness:"
   fastlist: false
 # - backend:  "onedrive"
 #   remote:   "TestOneDriveCn:"
 #   fastlist: false
 - backend:  "s3"
   remote:   "TestS3:"
   fastlist: true
 - backend:  "s3"
   remote:   "TestS3,directory_markers:"
   fastlist: true
 - backend:  "s3"
   remote:   "TestS3Minio:"
   fastlist: true
   ignore:
     - TestIntegration/FsMkdir/FsPutFiles/SetTier
     - TestIntegration/FsMkdir/FsEncoding/control_chars
     - TestIntegration/FsMkdir/FsEncoding/leading_LF
     - TestIntegration/FsMkdir/FsEncoding/leading_VT
     - TestIntegration/FsMkdir/FsEncoding/punctuation
     - TestIntegration/FsMkdir/FsEncoding/trailing_LF
     - TestIntegration/FsMkdir/FsEncoding/trailing_VT
 - backend:  "s3"
   remote:   "TestS3MinioEdge:"
   fastlist: true
   ignore:
     - TestIntegration/FsMkdir/FsPutFiles/SetTier
 - backend:  "s3"
   remote:   "TestS3Wasabi:"
   fastlist: true
   ignore:
     - TestIntegration/FsMkdir/FsEncoding/leading_CR
     - TestIntegration/FsMkdir/FsEncoding/leading_LF
     - TestIntegration/FsMkdir/FsEncoding/leading_HT
     - TestIntegration/FsMkdir/FsEncoding/leading_VT
     - TestIntegration/FsMkdir/FsPutFiles/FsPutStream/0
 # Disabled due to excessive rate limiting at DO which cause the tests never to pass
 # This hits the rate limit as documented here: https://www.digitalocean.com/docs/spaces/#limits
 # 2 COPYs per 5 minutes on any individual object in a Space
 # - backend:  "s3"
 #   remote:   "TestS3DigitalOcean:"
 #   fastlist: true
 #   ignore:
 #     - TestIntegration/FsMkdir/FsPutFiles/FsCopy
 #     - TestIntegration/FsMkdir/FsPutFiles/SetTier
 # - backend:  "s3"
 #   remote:   "TestS3Ceph:"
 #   fastlist: true
 #   ignore:
 #     - TestIntegration/FsMkdir/FsPutFiles/FsCopy
 #     - TestIntegration/FsMkdir/FsPutFiles/SetTier
 - backend:  "s3"
   remote:   "TestS3Alibaba:"
   fastlist: true
 # - backend:  "s3"
 #   remote:   "TestS3Qiniu:"
 #   fastlist: true
 #   ignore:
 #     - TestIntegration/FsMkdir/FsEncoding/control_chars
 #     - TestIntegration/FsMkdir/FsEncoding/leading_VT
 #     - TestIntegration/FsMkdir/FsEncoding/trailing_VT
 #     - TestIntegration/FsMkdir/FsPutFiles/FromRoot/ListR
 #     - TestIntegration/FsMkdir/FsPutFiles/SetTier
 #     - TestIntegration/FsMkdir/FsPutFiles/FsPutStream/0
 #     - TestIntegration/FsMkdir/FsPutFiles/Internal/Metadata
 - backend:  "s3"
   remote:   "TestS3R2:"
   fastlist: true
   ignore:
     - TestIntegration/FsMkdir/FsPutFiles/SetTier
 - backend:  "sftp"
   remote:   "TestSFTPOpenssh:"
   fastlist: false
 - backend:  "sftp"
   remote:   "TestSFTPRclone:"
   fastlist: false
 - backend:  "sftp"
   remote:   "TestSFTPRsyncNet:"
   fastlist: false
   ignore:
     - TestIntegration/FsMkdir/FsEncoding/trailing_space
     - TestIntegration/FsMkdir/FsEncoding/trailing_CR
     - TestIntegration/FsMkdir/FsEncoding/trailing_LF
     - TestIntegration/FsMkdir/FsEncoding/trailing_HT
     - TestIntegration/FsMkdir/FsEncoding/trailing_VT
     - TestIntegration/FsMkdir/FsEncoding/trailing_dot
     - TestIntegration/FsMkdir/FsEncoding/invalid_UTF-8
     - TestIntegration/FsMkdir/FsEncoding/URL_encoding
 - backend:  "sugarsync"
   remote:   "TestSugarSync:Test"
   fastlist: false
   ignore:
     - TestIntegration/FsMkdir/FsPutFiles/PublicLink
 - backend:  "swift"
   remote:   "TestSwiftAIO:"
   fastlist: true
 - backend:  "swift"
   remote:   "TestSwift:"
   fastlist: true
 # - backend:  "swift"
 #   remote:   "TestSwiftCeph:"
 #   fastlist: true
 #   ignore:
 #     - TestIntegration/FsMkdir/FsPutFiles/FsCopy
 - backend:  "yandex"
   remote:   "TestYandex:"
   fastlist: false
 - backend:  "ftp"
   remote:   "TestFTPProftpd:"
   fastlist: false
 - backend:  "ftp"
   remote:   "TestFTPPureftpd:"
   fastlist: false
 - backend:  "ftp"
   remote:   "TestFTPVsftpd:"
   fastlist: false
 - backend:  "ftp"
   remote:   "TestFTPRclone:"
   ignore:
     - "TestMultithreadCopy/{size:131071_streams:2}"
     - "TestMultithreadCopy/{size:131072_streams:2}"
     - "TestMultithreadCopy/{size:131073_streams:2}"
   fastlist: false
 - backend:  "box"
   remote:   "TestBox:"
   fastlist: false
 - backend:  "fichier"
   remote:   "TestFichier:"
   fastlist: false
   listretries: 5
   tests:
     - backend
 - backend:  "qingstor"
   remote:   "TestQingStor:"
   fastlist: false
   oneonly:  true
   cleanup:  true
   ignore:
     # This test fails because of a broken bucket in the account!
     - TestIntegration/FsMkdir/FsPutFiles/FromRoot/ListR
 - backend:  "azureblob"
   remote:   "TestAzureBlob:"
   fastlist: true
 - backend:  "azureblob"
   remote:   "TestAzureBlob,directory_markers:"
   fastlist: true
 - backend:  "pcloud"
   remote:   "TestPcloud:"
   fastlist: true
 - backend:  "pikpak"
   remote:   "TestPikPak:"
   fastlist: false
   ignore:
    #  fs/operations
     - TestCheckSum
     - TestHashSums/Md5
    #  fs/sync
     - TestSyncWithTrackRenames
    # integration
     - TestIntegration/FsMkdir/FsPutFiles/ObjectMimeType
 - backend:  "webdav"
   remote:   "TestWebdavNextcloud:"
   ignore:
     - TestIntegration/FsMkdir/FsEncoding/punctuation
     - TestIntegration/FsMkdir/FsEncoding/invalid_UTF-8
   fastlist: false
 - backend:  "webdav"
   remote:   "TestWebdavOwncloud:"
   ignore:
     - TestIntegration/FsMkdir/FsEncoding/punctuation
     - TestIntegration/FsMkdir/FsEncoding/invalid_UTF-8
     - TestIntegration/FsMkdir/FsPutFiles/FsCopy
     - TestCopyFileCopyDest
     - TestServerSideCopy
     - TestSyncCopyDest
   fastlist: false
 - backend:  "webdav"
   remote:   "TestWebdavRclone:"
   ignore:
     - TestFileReadAtZeroLength
   fastlist: false
 - backend:  "cache"
   remote:   "TestCache:"
   fastlist: false
 - backend:  "mega"
   remote:   "TestMega:"
   fastlist: false
   ignore:
     - TestIntegration/FsMkdir/FsPutFiles/PublicLink
     - TestDirRename
 - backend:  "opendrive"
   remote:   "TestOpenDrive:"
   fastlist: false
 - backend:  "union"
   remote:   "TestUnion:"
   fastlist: false
 - backend:  "koofr"
   remote:   "TestKoofr:"
   fastlist: false
 # - backend:  "koofr"
 #   remote:   "TestDigiStorage:"
 #   fastlist: false
 - backend:  "premiumizeme"
   remote:   "TestPremiumizeMe:"
   fastlist: false
 - backend:  "putio"
   remote:   "TestPutio:"
   fastlist: false
   extratime: 2.0
   ignore:
      - TestIntegration/FsMkdir/FsEncoding/URL_encoding
 - backend:  "sharefile"
   remote:   "TestSharefile:"
   fastlist: false
 - backend:  "sia"
   remote:   "TestSia:"
   fastlist: false
 - backend:  "mailru"
   remote:   "TestMailru:"
   subdir:   false
   fastlist: false
 - backend:  "seafile"
   remote:   "TestSeafileV6:"
   fastlist: false
   ignore:
     - TestIntegration/FsMkdir/FsPutFiles/FsDirMove
 - backend:  "seafile"
   remote:   "TestSeafile:"
   fastlist: true
 - backend:  "seafile"
   remote:   "TestSeafileEncrypted:"
   fastlist: true
 - backend:  "sia"
   remote:   "TestSia:"
   fastlist: false
 - backend:  "smb"
   remote:   "TestSMB:rclone"
   fastlist: false
 - backend:  "storj"
   remote:   "TestStorj:"
   fastlist: true
 - backend:  "zoho"
   remote:   "TestZoho:"
   fastlist: false
 - backend: "hdfs"
   remote: "TestHdfs:"
   fastlist: false
   ignore:
     - TestSyncUTFNorm
 - backend: "uptobox"
   remote: "TestUptobox:"
   fastlist: false
   ignore:
     - TestRWFileHandleWriteNoWrite
 - backend: "oracleobjectstorage"
   remote: "TestOracleObjectStorage:"
   fastlist: true
   ignore:
     - TestIntegration/FsMkdir/FsEncoding/control_chars
     - TestIntegration/FsMkdir/FsEncoding/leading_CR
     - TestIntegration/FsMkdir/FsEncoding/leading_LF
     - TestIntegration/FsMkdir/FsEncoding/trailing_CR
     - TestIntegration/FsMkdir/FsEncoding/trailing_LF
     - TestIntegration/FsMkdir/FsEncoding/leading_HT
